<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { API, ResponseHelper } from '@/utils/api'
import { DataUtils, User } from '@/utils/dataTypes'
import { DisplayNameUtils, ANOMALY_STATUS_NAMES } from '@/utils/constants'
import * as dayjs from 'dayjs'
import scannerUtils from '@/utils/scannerUtils.js'

// 用户信息
const userInfo = ref<User | null>(null)

// 统计数据
const stats = ref({
  todayTasks: 0,
  completedTasks: 0,
  pendingTasks: 0,
  totalTasks: 0
})

// 快捷功能
interface QuickAction {
  icon: string
  text: string
  action?: string
  path?: string
}

const quickActions = ref<QuickAction[]>([])

// 上报表单数据
const reportForm = reactive({
  qrCode: '',
  machineCode: '',
  machineName: '',
  reason: '',
  remark: ''
})

// 上报原因选项
const reportReasons = [
  { value: '断针', label: '断针' },
  { value: '皮带松动', label: '皮带松动' },
  { value: '维护保养', label: '维护保养' },
  { value: '其他', label: '其他' }
]

// 历史记录（从API获取）
const reportHistory = ref([])

// 获取异常上报历史记录
const getReportHistory = async () => {
  try {
    console.log('开始获取异常上报历史记录')
    
    // 调用API获取异常记录列表，只获取当前用户的记录
    const response = await API.anomalies.getList({ 
      page: 1, 
      page_size: 5  // 只显示最近5条记录
    })
    
    console.log('异常记录API响应:', response)
    
    if (ResponseHelper.isSuccess(response)) {
      const data = ResponseHelper.getData(response)
      
      // 转换数据格式以适配现有模板
      reportHistory.value = data.map((item: any) => ({
        id: item.id,
        machineCode: item.machine?.code || 'Unknown',
        machineName: item.machine?.name || '未知设备',
        reportTime: dayjs(item.created_at).format('YYYY-MM-DD HH:mm'),
        status: item.status,
        description: item.description,
        severity: item.severity,
        remark: item.remark
      }))
      
      console.log('转换后的历史记录:', reportHistory.value)
    } else {
      console.error('获取历史记录失败:', ResponseHelper.getErrorMessage(response))
      // 如果API调用失败，保持空数组，不显示错误给用户
    }
  } catch (error) {
    console.error('获取历史记录出错:', error)
    // 静默处理错误，保持空数组
  }
}


const getStatusInfo = (status: string) => {
  const statusMap = {
    pending: { text: ANOMALY_STATUS_NAMES.pending || '待维修', color: '#FF9800' },
    repairing: { text: ANOMALY_STATUS_NAMES.repairing || '维修中', color: '#2196F3' },
    completed: { text: ANOMALY_STATUS_NAMES.completed || '已完成', color: '#4CAF50' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999' }
}

// 获取用户信息
const getUserInfo = async () => {
  try {
    console.log('开始获取用户信息')
    
    // 首先检查本地是否有token，如果没有则跳转到登录页
    const token = uni.getStorageSync('access_token')
    if (!token) {
      console.log('未找到访问令牌，跳转到登录页')
      uni.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    // 调用API获取用户信息
    const response = await API.auth.getUserInfo()
    console.log('用户信息API响应:', response)
    
    if (ResponseHelper.isSuccess(response)) {
      const apiUserInfo = ResponseHelper.getData(response)
      
      // 转换为User类型并保存到本地存储
      userInfo.value = DataUtils.convertUser(apiUserInfo)
      uni.setStorageSync('userInfo', apiUserInfo)
      
      console.log('用户信息获取成功:', userInfo.value)
      
      // 根据角色设置快捷功能
      if (userInfo.value.isWeaver()) {
        quickActions.value = [
          { icon: '📱', text: '扫码上报', action: 'scan' },
          { icon: '📋', text: '上报记录', action: 'history' },
          { icon: '📊', text: '工作统计', action: 'stats' },
          { icon: '🔧', text: '设备状态', action: 'equipment' }
        ]
        
        // 织工用户获取历史记录
        await getReportHistory()
      } else if (userInfo.value.isMechanic()) {
        quickActions.value = [
          { icon: '🔧', text: '维修任务', path: '/pages/mechanic/home' },
          { icon: '📝', text: '维修记录', action: 'repair_history' },
          { icon: '📊', text: '工作统计', action: 'stats' },
          { icon: '⚙️', text: '设备管理', action: 'equipment' }
        ]
      }
    } else {
      throw new Error(ResponseHelper.getErrorMessage(response))
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    
    // API调用失败时，尝试使用本地存储的用户信息作为兜底
    const localUserInfo = uni.getStorageSync('userInfo')
    if (localUserInfo) {
      console.log('使用本地存储的用户信息作为兜底')
      userInfo.value = DataUtils.convertUser(localUserInfo)
      
      // 设置快捷功能（简化版）
      if (userInfo.value.isWeaver()) {
        quickActions.value = [
          { icon: '📱', text: '扫码上报', action: 'scan' },
          { icon: '📋', text: '上报记录', action: 'history' }
        ]
      } else if (userInfo.value.isMechanic()) {
        quickActions.value = [
          { icon: '🔧', text: '维修任务', path: '/pages/mechanic/home' },
          { icon: '📝', text: '维修记录', action: 'repair_history' }
        ]
      }
    } else {
      // 如果本地也没有用户信息，跳转到登录页
      console.log('本地也没有用户信息，跳转到登录页')
      uni.showToast({
        title: '请重新登录',
        icon: 'none'
      })
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
    }
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    if (userInfo.value?.isWeaver()) {
      // 织工统计：获取自己的异常上报统计
      const response = await API.anomalies.getList({ page: 1, page_size: 100 })
      if (ResponseHelper.isSuccess(response)) {
        const data = ResponseHelper.getData(response)
        const today = new Date().toDateString()
        
        // 计算今日任务数
        const todayTasks = data.filter((item: any) => 
          new Date(item.created_at).toDateString() === today
        ).length
        
        // 计算完成任务数
        const completedTasks = data.filter((item: any) => 
          item.status === 'completed'
        ).length
        
        // 计算待处理任务数
        const pendingTasks = data.filter((item: any) => 
          item.status === 'pending'
        ).length
        
        stats.value = {
          todayTasks,
          completedTasks,
          pendingTasks,
          totalTasks: data.length
        }
      } else {
        // API失败时使用默认值
        stats.value = { todayTasks: 0, completedTasks: 0, pendingTasks: 0, totalTasks: 0 }
      }
    } else if (userInfo.value?.isMechanic()) {
      // 机修工统计：可以获取维修相关统计
      // 暂时使用模拟数据，后续可以添加维修统计API
      stats.value = {
        todayTasks: 5,
        completedTasks: 3,
        pendingTasks: 2,
        totalTasks: 28
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    // 错误时使用默认值
    stats.value = { todayTasks: 0, completedTasks: 0, pendingTasks: 0, totalTasks: 0 }
  }
}

// 显示上报弹框
const showReportModal = (qrCode: string, machine: any) => {
  console.log('showReportModal called', qrCode, machine)
  reportForm.qrCode = qrCode
  reportForm.machineCode = machine?.code || qrCode
  reportForm.machineName = machine?.name || `机器-${qrCode}`
  reportForm.reason = ''
  reportForm.remark = ''

  console.log('reportForm filled:', reportForm)

  // 显示上报原因选择
  uni.showActionSheet({
    itemList: reportReasons.map(item => item.label),
    success: (res) => {
      const selectedReason = reportReasons[res.tapIndex]
      reportForm.reason = selectedReason.value

      if (selectedReason.value === '其他') {
        // 如果选择其他，显示输入框
        uni.showModal({
          title: '异常上报',
          content: `机台：${reportForm.machineName} (${reportForm.machineCode})\n上报原因：${reportForm.reason}\n\n请输入详细说明：`,
          editable: true,
          placeholderText: '请输入备注信息',
          success: (modalRes) => {
            if (modalRes.confirm) {
              reportForm.remark = modalRes.content || ''
              submitReport()
            }
          }
        })
      } else {
        // 直接确认上报
        uni.showModal({
          title: '确认上报',
          content: `机台：${reportForm.machineName} (${reportForm.machineCode})\n上报原因：${reportForm.reason}\n\n确认提交异常上报？`,
          success: (modalRes) => {
            if (modalRes.confirm) {
              submitReport()
            }
          }
        })
      }
    }
  })
}

// 关闭上报弹框（现在使用原生弹框，不需要此函数）
const closeReportModal = () => {
  // 使用原生弹框，无需手动关闭
}



// 提交上报
const submitReport = async () => {
  console.log('开始提交工作台异常上报')
  console.log('reportForm:', reportForm)
  
  if (!reportForm.reason) {
    uni.showToast({
      title: '请选择上报原因',
      icon: 'none'
    })
    return
  }

  if (reportForm.reason === '其他' && !reportForm.remark) {
    uni.showToast({
      title: '请填写备注信息',
      icon: 'none'
    })
    return
  }

  try {
    // 首先通过二维码获取机器信息
    let machineData = null
    try {
      const qrResponse = await API.machines.getByQRCode(reportForm.qrCode)
      if (ResponseHelper.isSuccess(qrResponse)) {
        machineData = ResponseHelper.getData(qrResponse).machine
      }
    } catch (error) {
      console.log('通过QR码获取机器信息失败，尝试使用机器码:', error)
      try {
        const codeResponse = await API.machines.getByCode(reportForm.machineCode)
        if (ResponseHelper.isSuccess(codeResponse)) {
          machineData = ResponseHelper.getData(codeResponse).machine
        }
      } catch (codeError) {
        console.log('通过机器码获取机器信息也失败:', codeError)
      }
    }

    // 如果没有获取到机器信息，使用默认ID
    const machineId = machineData?.id || 1

    // 构建符合后端API要求的数据结构
    const reportData = {
      machine_id: machineId,
      description: reportForm.reason === '其他' ? reportForm.remark : reportForm.reason,
      severity: 'medium', // 工作台快速上报默认为中等严重程度
      remark: reportForm.reason === '其他' ? '' : reportForm.remark || ''
    }

    console.log('准备发送的上报数据:', reportData)

    // 验证数据完整性
    if (!reportData.machine_id || !reportData.description || !reportData.severity) {
      throw new Error('数据不完整：' + JSON.stringify({
        machine_id: reportData.machine_id,
        description: reportData.description,
        severity: reportData.severity
      }))
    }

    // 调用异常上报接口
    const response = await API.anomalies.create(reportData)

    console.log('API响应:', response)

    if (ResponseHelper.isSuccess(response)) {
      uni.showToast({
        title: '上报成功',
        icon: 'success'
      })
      closeReportModal()
      
      // 刷新历史记录和统计数据
      if (userInfo.value?.isWeaver()) {
        await Promise.all([
          getReportHistory(),
          getStats()
        ])
      }
    } else {
      throw new Error(ResponseHelper.getErrorMessage(response))
    }
  } catch (error) {
    console.error('上报失败详细信息:', error)
    uni.showToast({
      title: error.message || '上报失败，请重试',
      icon: 'none'
    })
  }
}

// 扫码上报
const handleScanReport = async () => {
  try {
    // 根据用户角色选择扫码模式
    const scanMode = userInfo.value?.isWeaver() ? 'report' : 'repair'

    // 使用scannerUtils初始化扫码功能
    const initResult = await scannerUtils.initScanner((result) => {
      if (result.success) {
        console.log('扫码结果:', result)

        if (result.apiError) {
          // API调用失败，但仍可以跳转到相应页面
          uni.showToast({
            title: result.apiError,
            icon: 'none',
            duration: 2000
          })

          if (userInfo.value?.isWeaver()) {
            // 织工跳转到异常上报页面
            uni.navigateTo({
              url: `/pages/report/report?qrCode=${result.result}`
            })
          } else {
            // 机修工跳转到维修页面
            uni.navigateTo({
              url: `/pages/repair/repair?qrCode=${result.result}`
            })
          }
        } else if (result.data) {
          // API调用成功，根据角色处理不同逻辑
          if (userInfo.value?.isWeaver()) {
          console.log('result',result, userInfo.value)
            // 织工逻辑：直接显示上报弹框
            showReportModal(result.result, result.data.machine)
          } else {
            // 机修工逻辑：检查是否有异常需要维修
            const { machine, pending_anomalies, repairing_anomalies } = result.data

            if (pending_anomalies.length > 0 || repairing_anomalies.length > 0) {
              uni.navigateTo({
                url: `/pages/repair/repair?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data))}`
              })
            } else {
              uni.showModal({
                title: '提示',
                content: `机器 ${machine.name} 当前状态正常，无需维修`,
                showCancel: false
              })
            }
          }
        }
      } else {
        console.error('扫码失败:', result.error)
        uni.showToast({
          title: result.error || '扫码失败',
          icon: 'none'
        })
      }
    }, true, scanMode)

    if (initResult.success) {
      uni.showToast({
        title: '请扫描机器二维码',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('初始化扫码失败:', error)
    uni.showToast({
      title: '扫码功能初始化失败',
      icon: 'none'
    })
  }
}

// 查看报告详情
const viewReportDetail = (report: any) => {
  const statusInfo = getStatusInfo(report.status)
  let content = `机器：${report.machineName} (${report.machineCode})\n`
  content += `上报时间：${report.reportTime}\n`
  content += `状态：${statusInfo.text}\n`
  content += `严重程度：${getSeverityText(report.severity)}\n`
  content += `描述：${report.description}`
  
  if (report.remark) {
    content += `\n备注：${report.remark}`
  }
  
  uni.showModal({
    title: '上报详情',
    content: content,
    showCancel: false
  })
}

// 获取严重程度文字
const getSeverityText = (severity: string) => {
  const severityMap: Record<string, string> = {
    low: '轻微',
    medium: '中等', 
    high: '严重',
    critical: '紧急'
  }
  return severityMap[severity] || '未知'
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 使用新的API退出登录
        API.auth.logout()
      }
    }
  })
}

// 处理快捷功能点击
const handleQuickAction = (action: QuickAction) => {
  if (action.path) {
    uni.navigateTo({
      url: action.path
    })
  } else if (action.action === 'scan') {
    handleScanReport()
  } else {
    uni.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
}

onMounted(async () => {
  // 设置页面为活跃状态
  scannerUtils.setPageActive(true)
  if (uni.getSystemInfoSync().platform === 'android') {
    handleScanReport()
  }
  await getUserInfo()  // 等待用户信息加载完成
  await getStats()     // 等待统计数据加载完成
})

// 页面卸载时清理扫码资源
onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script lang="ts">
export default {
  onShow() {
    console.log('工作台页面显示，激活扫码功能')
    scannerUtils.setPageActive(true)
  },
  onHide() {
    console.log('工作台页面隐藏，停用扫码功能')
    scannerUtils.setPageActive(false)
  },
  onUnload() {
    console.log('工作台页面卸载，清理扫码资源')
    scannerUtils.destroy()
  }
}
</script>

<template>
  <view class="workspace-container">
    <!-- 欢迎信息 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <view class="welcome-info">
          <text class="welcome-text">你好，{{ userInfo?.name || userInfo?.username }}</text>
          <text class="role-text">{{ userInfo?.getRoleDisplayName() }}工作台</text>
        </view>
        <view class="logout-btn" @click="handleLogout">
          <text class="logout-text">退出</text>
        </view>
      </view>
    </view>

    <!-- 统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stat-item">
          <text class="stat-number">{{ stats.todayTasks }}</text>
          <text class="stat-label">今日任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.completedTasks }}</text>
          <text class="stat-label">已完成</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.pendingTasks }}</text>
          <text class="stat-label">待处理</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ stats.totalTasks }}</text>
          <text class="stat-label">总任务</text>
        </view>
      </view>
    </view>

    <!-- 快捷功能 -->
    <view class="quick-actions">
      <view class="section-title">快捷功能</view>
      <view class="actions-grid">
        <view 
          class="action-item" 
          v-for="(action, index) in quickActions" 
          :key="index"
          @click="handleQuickAction(action)"
        >
          <view class="action-icon">{{ action.icon }}</view>
          <text class="action-text">{{ action.text }}</text>
        </view>
      </view>
    </view>

    <!-- 织工历史记录 -->
    <view class="recent-section" v-if="userInfo?.isWeaver()">
      <view class="section-header">
        <view class="section-title">最近上报</view>
        <!-- <text class="view-all">查看全部</text> -->
      </view>
      
      <view class="history-list" v-if="reportHistory.length > 0">
        <view 
          class="history-item" 
          v-for="item in reportHistory" 
          :key="item.id"
          @click="viewReportDetail(item)"
        >
          <view class="machine-info">
            <text class="machine-name">{{ item.machineName }}</text>
            <text class="machine-code">{{ item.machineCode }}</text>
          </view>
          <view class="report-info">
            <text class="report-time">{{ item.reportTime }}</text>
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getStatusInfo(item.status).color }"
            >
              <text class="status-text">{{ getStatusInfo(item.status).text }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 暂无记录提示 -->
      <view class="empty-state" v-else>
        <text class="empty-text">暂无异常上报记录</text>
      </view>
    </view>

    <!-- 机修工最近活动 -->
    <view class="recent-section" v-else>
      <view class="section-title">最近活动</view>
      <view class="activity-list">
        <view class="activity-item">
          <view class="activity-icon">🔧</view>
          <view class="activity-content">
            <text class="activity-title">设备维修完成</text>
            <text class="activity-time">2小时前</text>
          </view>
        </view>
        
        <view class="activity-item">
          <view class="activity-icon">📱</view>
          <view class="activity-content">
            <text class="activity-title">异常上报</text>
            <text class="activity-time">4小时前</text>
          </view>
        </view>
        
        <view class="activity-item">
          <view class="activity-icon">✅</view>
          <view class="activity-content">
            <text class="activity-title">任务完成</text>
            <text class="activity-time">昨天</text>
          </view>
        </view>
      </view>
    </view>
  </view>


</template>

<style lang="scss">
.workspace-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.welcome-section {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(33, 150, 243, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-info {
  display: flex;
  flex-direction: column;
}

.welcome-text {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.role-text {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  
  &:active {
    background: rgba(255, 255, 255, 0.3);
  }
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.stats-section {
  margin-bottom: 30rpx;
}

.stats-card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.quick-actions {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  
  &:active {
    transform: scale(0.95);
    background: #f8f8f8;
  }
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.action-text {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.recent-section {
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.view-all {
  font-size: 24rpx;
  color: #2196F3;
}

.history-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.history-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f8f8;
  }
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-code {
  font-size: 24rpx;
  color: #666666;
}

.report-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.report-time {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

.activity-list {
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.activity-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  margin-right: 30rpx;
}

.activity-content {
  flex: 1;
}

.activity-title {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.activity-time {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

.empty-state {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}
</style>
