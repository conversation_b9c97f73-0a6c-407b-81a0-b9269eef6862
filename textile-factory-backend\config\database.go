package config

import (
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"log"
	"os"
	"time"
	"textile-factory-backend/constants"
	"textile-factory-backend/models"
	"textile-factory-backend/utils"
)

var DB *gorm.DB

func InitDB() error {
	dsn := os.Getenv("DB_DSN")
	if dsn == "" {
		// 使用远程MySQL配置，添加连接超时参数
		dsn = "root:passwordpassword@tcp(192.168.1.66:3306)/textile_factory?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s&readTimeout=30s&writeTimeout=30s"
	}

	log.Printf("Attempting to connect to database with DSN: %s", dsn)

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Printf("Failed to connect to database: %v", err)
		return err
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		log.Printf("Failed to get database instance: %v", err)
		return err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	log.Println("Database connection established successfully")

	// 自动迁移数据库表
	log.Println("Starting database migration...")
	err = DB.AutoMigrate(
		&models.User{},
		&models.Machine{},
		&models.Anomaly{},
		&models.Repair{},
		&models.RefreshToken{},
	)
	if err != nil {
		log.Printf("Database migration failed: %v", err)
		return err
	}

	log.Println("Database migration completed successfully")

	// 创建默认管理员用户
	log.Println("Creating default users and machines...")
	createDefaultUsers()
	createDefaultMachines()
	log.Println("Default data creation completed")

	return nil
}

func createDefaultUsers() {
	// 生成密码哈希
	hashedPassword, err := utils.HashPassword("123456")
	if err != nil {
		log.Printf("Failed to hash password: %v", err)
		return
	}

	// 创建或更新默认织工用户
	var weaver models.User
	result := DB.Where("username = ?", "weaver01").First(&weaver)
	if result.Error == gorm.ErrRecordNotFound {
		weaver = models.User{
			Username: "weaver01",
			Password: hashedPassword,
			Role:     constants.UserRoleWeaver,
			Name:     "张织工",
		}
		DB.Create(&weaver)
		log.Printf("Created default weaver user: weaver01")
	} else {
		// 更新现有用户的密码（如果密码是旧的假哈希）
		if weaver.Password == "$2a$10$8X8X8X8X8X8X8X8X8X8X8.8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8" {
			DB.Model(&weaver).Updates(map[string]interface{}{
				"password": hashedPassword,
				"role":     constants.UserRoleWeaver,
			})
			log.Printf("Updated weaver01 password and role with enum")
		}
	}

	// 创建或更新默认机修工用户
	var mechanic models.User
	result = DB.Where("username = ?", "mechanic01").First(&mechanic)
	if result.Error == gorm.ErrRecordNotFound {
		mechanic = models.User{
			Username: "mechanic01",
			Password: hashedPassword,
			Role:     constants.UserRoleMechanic,
			Name:     "李机修",
		}
		DB.Create(&mechanic)
		log.Printf("Created default mechanic user: mechanic01")
	} else {
		// 更新现有用户的密码（如果密码是旧的假哈希）
		if mechanic.Password == "$2a$10$8X8X8X8X8X8X8X8X8X8X8.8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8" {
			DB.Model(&mechanic).Updates(map[string]interface{}{
				"password": hashedPassword,
				"role":     constants.UserRoleMechanic,
			})
			log.Printf("Updated mechanic01 password and role with enum")
		}
	}
}

func createDefaultMachines() {
	machines := []models.Machine{
		{Code: "M001", Name: "织机001", Location: "A区1号位", QRCode: "QR_M001", Status: constants.MachineStatusNormal},
		{Code: "M002", Name: "织机002", Location: "A区2号位", QRCode: "QR_M002", Status: constants.MachineStatusNormal},
		{Code: "M003", Name: "织机003", Location: "B区1号位", QRCode: "QR_M003", Status: constants.MachineStatusNormal},
		{Code: "M004", Name: "织机004", Location: "B区2号位", QRCode: "QR_M004", Status: constants.MachineStatusNormal},
		{Code: "M005", Name: "织机005", Location: "C区1号位", QRCode: "QR_M005", Status: constants.MachineStatusNormal},
	}

	for _, machine := range machines {
		var existing models.Machine
		result := DB.Where("code = ?", machine.Code).First(&existing)
		if result.Error == gorm.ErrRecordNotFound {
			DB.Create(&machine)
		}
	}
}
