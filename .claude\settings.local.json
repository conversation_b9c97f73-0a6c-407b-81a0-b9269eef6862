{"permissions": {"allow": ["Bash(cd /mnt/d/learn/uniapptest/textile-factory-backend)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(wget:*)", "Bash(sudo tar:*)", "Bash(rm:*)", "Bash(tar:*)", "<PERSON><PERSON>(touch:*)", "Bash(go build:*)", "<PERSON><PERSON>(go run:*)", "Bash(go mod:*)", "Bash(go get:*)", "<PERSON><PERSON>(mysql:*)", "Bash(/snap/go/10907/bin/go version)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(./textile-factory-backend)", "Bash(cat server.log)", "Bash(systemctl status:*)", "Bash(service mysql:*)", "Bash(systemctl status mysqld)", "Bash(service mysqld:*)"], "deny": []}}