package controllers

import (
	"strconv"
	"time"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/response"
	"github.com/gin-gonic/gin"
)

type RepairRequest struct {
	AnomalyID uint   `json:"anomaly_id" binding:"required"`
	Process   string `json:"process"`
	Parts     string `json:"parts"`
}

type CompleteRepairRequest struct {
	Process  string `json:"process" binding:"required"`
	Parts    string `json:"parts"`
	Duration int    `json:"duration" binding:"required"`
}

func StartRepair(c *gin.Context) {
	var req RepairRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	mechanicID, _ := c.Get("user_id")
	
	// 验证异常记录是否存在且状态正确
	var anomaly models.Anomaly
	if err := config.DB.First(&anomaly, req.AnomalyID).Error; err != nil {
		response.NotFound(c, "异常记录不存在")
		return
	}
	
	if anomaly.Status != "待维修" {
		response.BadRequest(c, "该异常已在维修中或已完成")
		return
	}
	
	// 检查是否已有维修记录
	var existingRepair models.Repair
	if err := config.DB.Where("anomaly_id = ?", req.AnomalyID).First(&existingRepair).Error; err == nil {
		response.BadRequest(c, "该异常已有维修记录")
		return
	}
	
	// 创建维修记录
	repair := models.Repair{
		AnomalyID:  req.AnomalyID,
		MechanicID: mechanicID.(uint),
		StartTime:  time.Now(),
		Process:    req.Process,
		Parts:      req.Parts,
		Status:     "进行中",
	}
	
	if err := config.DB.Create(&repair).Error; err != nil {
		response.InternalServerError(c, "创建维修记录失败")
		return
	}
	
	// 更新异常状态为维修中
	config.DB.Model(&anomaly).Update("status", "维修中")
	
	// 更新机器状态为维修中
	config.DB.Model(&models.Machine{}).Where("id = ?", anomaly.MachineID).Update("status", "维修中")
	
	// 返回完整的维修记录
	config.DB.Preload("Anomaly").Preload("Mechanic").First(&repair, repair.ID)
	
	response.SuccessWithMessage(c, "维修开始成功", repair)
}

func CompleteRepair(c *gin.Context) {
	repairID := c.Param("id")
	
	var req CompleteRepairRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	mechanicID, _ := c.Get("user_id")
	
	var repair models.Repair
	if err := config.DB.Where("id = ? AND mechanic_id = ?", repairID, mechanicID).First(&repair).Error; err != nil {
		response.NotFound(c, "维修记录不存在或无权限操作")
		return
	}
	
	if repair.Status != "进行中" {
		response.BadRequest(c, "该维修记录已完成")
		return
	}
	
	// 更新维修记录
	endTime := time.Now()
	updates := map[string]interface{}{
		"process":  req.Process,
		"parts":    req.Parts,
		"duration": req.Duration,
		"end_time": endTime,
		"status":   "已完成",
	}
	
	if err := config.DB.Model(&repair).Updates(updates).Error; err != nil {
		response.InternalServerError(c, "更新维修记录失败")
		return
	}
	
	// 获取关联的异常记录
	var anomaly models.Anomaly
	config.DB.First(&anomaly, repair.AnomalyID)
	
	// 更新异常状态为已完成
	config.DB.Model(&anomaly).Update("status", "已完成")
	
	// 更新机器状态为正常
	config.DB.Model(&models.Machine{}).Where("id = ?", anomaly.MachineID).Update("status", "正常")
	
	// 返回更新后的维修记录
	config.DB.Preload("Anomaly").Preload("Mechanic").First(&repair, repair.ID)
	
	response.SuccessWithMessage(c, "维修完成", repair)
}

func GetRepairList(c *gin.Context) {
	var repairs []models.Repair
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	status := c.Query("status")
	
	query := config.DB.Model(&models.Repair{}).
		Preload("Anomaly").
		Preload("Anomaly.Machine").
		Preload("Mechanic")
	
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	// 机修工只能查看自己的维修记录
	role, _ := c.Get("role")
	if role == "机修工" {
		mechanicID, _ := c.Get("user_id")
		query = query.Where("mechanic_id = ?", mechanicID)
	}
	
	var total int64
	query.Count(&total)
	
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&repairs)
	
	if result.Error != nil {
		response.InternalServerError(c, "查询维修列表失败")
		return
	}
	
	response.Pagination(c, repairs, total, page, pageSize)
}

func GetRepairDetail(c *gin.Context) {
	id := c.Param("id")
	
	var repair models.Repair
	result := config.DB.Where("id = ?", id).
		Preload("Anomaly").
		Preload("Anomaly.Machine").
		Preload("Anomaly.User").
		Preload("Mechanic").
		First(&repair)
	
	if result.Error != nil {
		response.NotFound(c, "维修记录不存在")
		return
	}
	
	// 机修工只能查看自己的维修记录
	role, _ := c.Get("role")
	mechanicID, _ := c.Get("user_id")
	if role == "机修工" && repair.MechanicID != mechanicID.(uint) {
		response.Forbidden(c, "没有权限查看此记录")
		return
	}
	
	response.Success(c, repair)
}