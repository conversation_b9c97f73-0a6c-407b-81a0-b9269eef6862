<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import scannerUtils from '@/utils/scannerUtils.js'
import { API, ResponseHelper } from '@/utils/api.js'

// 用户信息
const userInfo = ref<any>({})

// 历史记录
const reportHistory = ref([])
const isLoadingHistory = ref(false)

// 获取状态文本和颜色
const getStatusInfo = (status: string) => {
  const statusMap = {
    '待维修': { text: '待维修', color: '#FF9800' },
    '维修中': { text: '维修中', color: '#2196F3' },
    '已完成': { text: '已完成', color: '#4CAF50' }
  }
  return statusMap[status as keyof typeof statusMap] || { text: '未知', color: '#999999' }
}

// 扫码上报
const handleScanReport = async () => {
  try {
    // 使用上报模式初始化扫码功能
    const initResult = await scannerUtils.initScanner((result) => {
      if (result.success) {
        console.log('扫码结果:', result)
        
        if (result.apiError) {
          // API调用失败，但仍可以跳转到上报页面
          uni.showToast({
            title: result.apiError,
            icon: 'none',
            duration: 2000
          })
          // 跳转到异常上报页面，传递机器码
          uni.navigateTo({
            url: `/pages/report/report?qrCode=${result.result}`
          })
        } else if (result.data) {
          // API调用成功，检查是否可以上报
          if (result.data.can_report) {
            // 可以上报异常
            uni.navigateTo({
              url: `/pages/report/report?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data.machine))}`
            })
          } else {
            // 机器已有未完成的异常
            uni.showModal({
              title: '提示',
              content: result.data.message || '该机器已有未完成的异常报告',
              showCancel: true,
              cancelText: '取消',
              confirmText: '查看详情',
              success: (res) => {
                if (res.confirm && result.data.pending_anomaly) {
                  // 查看现有异常详情
                  uni.navigateTo({
                    url: `/pages/report/detail?id=${result.data.pending_anomaly.id}`
                  })
                }
              }
            })
          }
        }
      } else {
        console.error('扫码失败:', result.error)
        uni.showToast({
          title: result.error || '扫码失败',
          icon: 'none'
        })
      }
    }, true, 'report') // 设置为上报模式
    
    if (initResult.success) {
      uni.showToast({
        title: '请扫描机器二维码',
        icon: 'none',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('初始化扫码失败:', error)
    uni.showToast({
      title: '扫码功能初始化失败',
      icon: 'none'
    })
  }
}

// 查看报告详情
const viewReportDetail = (report: any) => {
  uni.navigateTo({
    url: `/pages/report/detail?id=${report.id}`
  })
}

// 加载历史记录
const loadReportHistory = async () => {
  try {
    isLoadingHistory.value = true
    const response = await API.anomalies.getList({
      page: 1,
      page_size: 5 // 只显示最近5条
    })
    
    // 使用ResponseHelper处理响应
    const paginationData = ResponseHelper.getPagination(response)
    console.log('paginationData',paginationData)
    if (paginationData.data) {
      reportHistory.value = paginationData.data.map(anomaly => ({
        id: anomaly.id,
        machineCode: anomaly.machine?.code || '',
        machineName: anomaly.machine?.name || '',
        reportTime: new Date(anomaly.created_at).toLocaleString(),
        status: anomaly.status,
        description: anomaly.description
      }))
    }
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: error.message || '加载历史记录失败',
      icon: 'none'
    })
  } finally {
    isLoadingHistory.value = false
  }
}

// 获取用户信息
const loadUserInfo = async () => {
  try {
    const response = await API.auth.getUserInfo()
    
    // 使用ResponseHelper处理响应
    const userData = ResponseHelper.getData(response)
    
    if (userData) {
      userInfo.value = userData
      uni.setStorageSync('userInfo', userData)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    // 如果获取用户信息失败，可能是token过期，跳转到登录页
    uni.reLaunch({
      url: '/pages/login/login'
    })
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // 清除本地存储的认证信息
        uni.removeStorageSync('userInfo')
        uni.removeStorageSync('token')
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 页面加载时初始化
onMounted(async () => {
  // 设置页面为活跃状态
  scannerUtils.setPageActive(true)
  
  // 检查本地存储的用户信息
  const user = uni.getStorageSync('userInfo')
  if (user) {
    userInfo.value = user
  }
  
  // 加载最新的用户信息和历史记录
  await loadUserInfo()
  await loadReportHistory()
})

// 页面卸载时清理扫码资源
onUnmounted(() => {
  scannerUtils.destroy()
})
</script>

<script lang="ts">
export default {
  onShow() {
    console.log('织工页面显示，激活扫码功能')
    // 这里不能直接使用scannerUtils，需要通过全局方式访问
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(true)
    }
  },
  onHide() {
    console.log('织工页面隐藏，停用扫码功能')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.setPageActive(false)
    }
  },
  onUnload() {
    console.log('织工页面卸载，清理扫码资源')
    if (typeof scannerUtils !== 'undefined') {
      scannerUtils.destroy()
    }
  }
}
</script>

<template>
  <view class="worker-home">
    <!-- 用户信息头部 -->
    <<!-- view class="user-header">
      <view class="user-info">
        <view class="avatar">
          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>
        </view>
        <view class="user-details">
          <text class="username">{{ userInfo.username }}</text>
          <text class="role">织工</text>
        </view>
      </view>
      <view class="logout-btn" @click="handleLogout">
        <text class="logout-text">退出</text>
      </view>
    </view> -->
    
    <!-- 快速操作区域 -->
    <view class="quick-actions">
      <view class="scan-card" @click="handleScanReport">
        <view class="scan-icon">
          <text class="icon">📱</text>
        </view>
        <view class="scan-content">
          <text class="scan-title">扫码上报异常</text>
          <text class="scan-desc">扫描机器二维码快速上报故障</text>
        </view>
        <view class="scan-arrow">
          <text class="arrow">→</text>
        </view>
      </view>
    </view>
    
    <!-- 统计信息 -->
    <view class="stats-section">
      <text class="section-title">今日统计</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">3</text>
          <text class="stat-label">今日上报</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">1</text>
          <text class="stat-label">待维修</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">2</text>
          <text class="stat-label">已完成</text>
        </view>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="section-header">
        <text class="section-title">最近上报</text>
        <text class="view-all" @click="loadReportHistory">{{ isLoadingHistory ? '加载中...' : '刷新' }}</text>
      </view>
      
      <view class="history-list" v-if="reportHistory.length > 0">
        <view 
          class="history-item" 
          v-for="item in reportHistory" 
          :key="item.id"
          @click="viewReportDetail(item)"
        >
          <view class="machine-info">
            <text class="machine-name">{{ item.machineName }}</text>
            <text class="machine-code">{{ item.machineCode }}</text>
          </view>
          <view class="report-info">
            <text class="report-time">{{ item.reportTime }}</text>
            <view 
              class="status-tag" 
              :style="{ backgroundColor: getStatusInfo(item.status).color }"
            >
              <text class="status-text">{{ getStatusInfo(item.status).text }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="empty-state" v-else-if="!isLoadingHistory">
        <text class="empty-text">暂无上报记录</text>
        <text class="empty-desc">扫码上报机器异常后，记录将显示在这里</text>
      </view>
      
      <view class="loading-state" v-if="isLoadingHistory">
        <text class="loading-text">加载中...</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.worker-home {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 40rpx;
}

.user-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 40rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.avatar-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.role {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logout-btn {
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
}

.logout-text {
  font-size: 24rpx;
  color: #ffffff;
}

.quick-actions {
  padding: 30rpx;
}

.scan-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  
  &:active {
    transform: scale(0.98);
  }
}

.scan-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.icon {
  font-size: 36rpx;
}

.scan-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.scan-desc {
  font-size: 24rpx;
  color: #666666;
}

.scan-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow {
  font-size: 24rpx;
  color: #999999;
}

.stats-section {
  padding: 0 30rpx 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 24rpx;
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stat-item {
  flex: 1;
  background: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #2196F3;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666666;
}

.history-section {
  padding: 0 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.view-all {
  font-size: 24rpx;
  color: #2196F3;
}

.history-list {
  background: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.history-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background: #f8f8f8;
  }
}

.machine-info {
  display: flex;
  flex-direction: column;
}

.machine-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.machine-code {
  font-size: 24rpx;
  color: #666666;
}

.report-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.report-time {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-text {
  font-size: 20rpx;
  color: #ffffff;
  font-weight: 500;
}

.empty-state, .loading-state {
  padding: 60rpx 30rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-text, .loading-text {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.5;
}
</style>
