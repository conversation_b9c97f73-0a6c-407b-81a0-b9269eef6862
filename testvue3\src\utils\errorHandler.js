/**
 * 通用错误处理工具
 */
import { ResponseHelper, RESPONSE_CODES } from './api.js'

/**
 * 错误处理类
 */
export class ErrorHandler {
  /**
   * 处理API错误
   * @param {Error} error 错误对象
   * @param {Object} options 选项
   * @returns {string} 错误消息
   */
  static handleApiError(error, options = {}) {
    const {
      showToast = true,
      defaultMessage = '操作失败',
      duration = 2000
    } = options

    let message = error?.message || defaultMessage

    // 根据错误类型进行特殊处理
    if (error?.message) {
      if (error.message.includes('网络')) {
        message = '网络连接失败，请检查网络后重试'
      } else if (error.message.includes('超时')) {
        message = '请求超时，请重试'
      } else if (error.message.includes('登录已过期')) {
        message = '登录已过期，请重新登录'
        // 已过期的处理在api.js中已经处理了，这里只需要显示消息
      }
    }

    if (showToast) {
      uni.showToast({
        title: message,
        icon: 'none',
        duration
      })
    }

    return message
  }

  /**
   * 处理表单验证错误
   * @param {Object} formData 表单数据
   * @param {Object} rules 验证规则
   * @returns {Object} 验证结果
   */
  static validateForm(formData, rules) {
    const errors = {}
    let isValid = true

    for (const field in rules) {
      const rule = rules[field]
      const value = formData[field]

      if (rule.required && (!value || (typeof value === 'string' && !value.trim()))) {
        errors[field] = rule.message || `${field}不能为空`
        isValid = false
        continue
      }

      if (value && rule.pattern && !rule.pattern.test(value)) {
        errors[field] = rule.message || `${field}格式不正确`
        isValid = false
        continue
      }

      if (value && rule.minLength && value.length < rule.minLength) {
        errors[field] = rule.message || `${field}长度不能少于${rule.minLength}个字符`
        isValid = false
        continue
      }

      if (value && rule.maxLength && value.length > rule.maxLength) {
        errors[field] = rule.message || `${field}长度不能超过${rule.maxLength}个字符`
        isValid = false
        continue
      }
    }

    return {
      isValid,
      errors,
      firstError: isValid ? null : Object.values(errors)[0]
    }
  }

  /**
   * 显示表单验证错误
   * @param {Object} validation 验证结果
   */
  static showValidationError(validation) {
    if (!validation.isValid && validation.firstError) {
      uni.showToast({
        title: validation.firstError,
        icon: 'none',
        duration: 2000
      })
    }
  }

  /**
   * 安全执行异步操作
   * @param {Function} asyncFn 异步函数
   * @param {Object} options 选项
   * @returns {Promise} 执行结果
   */
  static async safeExecute(asyncFn, options = {}) {
    const {
      loadingRef = null,
      errorHandler = this.handleApiError,
      showLoading = false,
      loadingText = '处理中...'
    } = options

    try {
      if (loadingRef) {
        loadingRef.value = true
      }

      if (showLoading) {
        uni.showLoading({
          title: loadingText,
          mask: true
        })
      }

      const result = await asyncFn()
      
      if (showLoading) {
        uni.hideLoading()
      }

      return {
        success: true,
        data: result,
        error: null
      }
    } catch (error) {
      console.error('SafeExecute error:', error)
      
      if (showLoading) {
        uni.hideLoading()
      }

      if (errorHandler) {
        errorHandler(error)
      }

      return {
        success: false,
        data: null,
        error
      }
    } finally {
      if (loadingRef) {
        loadingRef.value = false
      }
    }
  }
}

/**
 * 常用验证规则
 */
export const ValidationRules = {
  required: (message) => ({
    required: true,
    message
  }),

  minLength: (length, message) => ({
    minLength: length,
    message
  }),

  maxLength: (length, message) => ({
    maxLength: length,
    message
  }),

  pattern: (pattern, message) => ({
    pattern,
    message
  }),

  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入正确的邮箱地址'
  },

  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号码'
  },

  username: {
    pattern: /^[a-zA-Z0-9_]{3,20}$/,
    message: '用户名只能包含字母、数字、下划线，长度3-20位'
  }
}

export default ErrorHandler