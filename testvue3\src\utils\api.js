/**
 * API 接口配置和调用工具
 */

// API 基础配置
const API_CONFIG = {
  BASE_URL: 'http://************:8080/api/v1', // 后端服务器地址
  TIMEOUT: 10000
}

// 响应码定义
export const RESPONSE_CODES = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
}

// 根据环境判断使用不同的base url
function getBaseUrl() {
  // #ifdef H5
  return 'http://************:8080/api/v1'
  // #endif
  // #ifdef APP-PLUS
  return 'http://************:8080/api/v1' // 请替换为实际的服务器IP
  // #endif
  // #ifdef MP-WEIXIN
  return 'https://your-domain.com/api/v1' // 微信小程序需要使用https
  // #endif
  return API_CONFIG.BASE_URL
}

// 存储token
let accessToken = uni.getStorageSync('access_token') || ''
let refreshToken = uni.getStorageSync('refresh_token') || ''

/**
 * 设置认证token
 */
export function setTokens(access_token, refresh_token) {
  accessToken = access_token
  refreshToken = refresh_token
  uni.setStorageSync('access_token', access_token)
  uni.setStorageSync('refresh_token', refresh_token)
}

/**
 * 获取access token
 */
export function getAccessToken() {
  return accessToken || uni.getStorageSync('access_token')
}

/**
 * 获取refresh token
 */
export function getRefreshToken() {
  return refreshToken || uni.getStorageSync('refresh_token')
}

/**
 * 向后兼容的getAuthToken函数
 */
export function getAuthToken() {
  return getAccessToken()
}

/**
 * 向后兼容的setAuthToken函数
 */
export function setAuthToken(token) {
  // 如果传入的是旧格式token，存储为access_token
  setTokens(token, '')
}

/**
 * 清除认证信息
 */
export function clearAuth() {
  accessToken = ''
  refreshToken = ''
  uni.removeStorageSync('access_token')
  uni.removeStorageSync('refresh_token')
  uni.removeStorageSync('token') // 兼容旧版本
  uni.removeStorageSync('userInfo')
}

/**
 * 刷新access token
 */
async function refreshAccessToken() {
  const refresh_token = getRefreshToken()
  if (!refresh_token) {
    throw new Error('No refresh token available')
  }
  
  try {
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: `${getBaseUrl()}/refresh-token`,
        method: 'POST',
        data: { refresh_token },
        header: { 'Content-Type': 'application/json' },
        success: resolve,
        fail: reject
      })
    })
    
    if (response.statusCode === 200 && response.data.code === RESPONSE_CODES.SUCCESS) {
      const newAccessToken = response.data.data.access_token
      setTokens(newAccessToken, refresh_token) // 保留原refresh token
      return newAccessToken
    } else {
      throw new Error('Refresh token failed')
    }
  } catch (error) {
    console.error('刷新token失败:', error)
    clearAuth()
    throw error
  }
}
/**
 * 规范化URL，添加必要的斜杠
 */
function normalizeUrl(baseUrl, path) {
  // 确保baseUrl不以斜杠结尾
  const base = baseUrl.replace(/\/$/, '')
  // 确保path以斜杠开始
  const normalizedPath = path.startsWith('/') ? path : '/' + path
  return base + normalizedPath
}

/**
 * 统一的HTTP请求方法
 */
function request(options) {
  return new Promise(async (resolve, reject) => {
    const token = getAuthToken()
    const baseUrl = getBaseUrl()
    
    // 添加详细的请求日志
    console.log('发起API请求:', {
      url: `${baseUrl}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      hasToken: !!token
    })
    
    uni.request({
      url: normalizeUrl(baseUrl, options.url),
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
        ...(options.header || {})
      },
      timeout: API_CONFIG.TIMEOUT,
      success: async(res) => {
        console.log('API请求成功:', res)
        
        // 检查HTTP状态码
        if (res.statusCode === 200) {
          // 检查业务响应格式
          const response = res.data
          
          // 如果响应包含统一格式的code字段
          if (response && typeof response.code !== 'undefined') {
            if (response.code === RESPONSE_CODES.SUCCESS) {
              // 业务成功，返回完整响应（包含分页信息）
              resolve(response)
            } else {
              // 业务失败，抛出错误信息
              const errorMsg = response.message || '操作失败'
              reject(new Error(errorMsg))
            }
          } else {
            // 兼容旧格式，直接返回response
            resolve(response)
          }
        } else if (res.statusCode === 401) {
          console.log('res',res)
          // token过期，尝试刷新token
          try {
            if (getRefreshToken()) {
              console.log('尝试刷新access token')
              await refreshAccessToken()
              // 重新发起请求
              const newToken = getAccessToken()
              const retryOptions = {
                ...options,
                header: {
                  ...options.header,
                  'Authorization': `Bearer ${newToken}`
                }
              }
              // 递归调用，但要避免无限循环
              if (!options._isRetry) {
                retryOptions._isRetry = true
                const retryResult = await request(retryOptions)
                resolve(retryResult)
                return
              }
            }
            throw new Error('Token refresh failed')
          } catch (refreshError) {
            console.error('Token刷新失败:', refreshError)
            clearAuth()
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none'
            })
            // 跳转到登录页
            uni.reLaunch({
              url: '/pages/login/login'
            })
            reject(new Error(res.data.error || '登录已过期'))
          }
        } else {
          // 其他HTTP错误
          const response = res.data
          let errorMsg = `请求失败 (${res.statusCode})`
          
          // 尝试从响应中获取错误信息
          if (response) {
            if (response.message) {
              errorMsg = response.message
            } else if (response.error) {
              errorMsg = response.error
            }
          }
          
          reject(new Error(errorMsg))
        }
      },
      fail: (err) => {
        console.error('API请求失败:', err)
        let errorMsg = '网络请求失败'
        
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接'
          } else if (err.errMsg.includes('fail')) {
            if (err.errMsg.includes('localhost') || err.errMsg.includes('127.0.0.1')) {
              errorMsg = '无法连接到本地服务器，请确保后端服务已启动'
            } else {
              errorMsg = '无法连接到服务器，请检查网络和服务器状态'
            }
          } else if (err.errMsg.includes('abort')) {
            errorMsg = '请求被中断，请检查网络连接'
          }
        }
        
        reject(new Error(errorMsg))
      }
    })
  })
}

/**
 * 处理API响应数据的工具函数
 */
export const ResponseHelper = {
  /**
   * 获取响应数据
   * @param {Object} response API响应
   * @returns {any} 数据
   */
  getData(response) {
    return response?.data || null
  },

  /**
   * 获取分页数据
   * @param {Object} response API响应
   * @returns {Object} 分页信息
   */
  getPagination(response) {
    if (response?.total !== undefined) {
      return {
        data: response.data || [],
        total: response.total || 0,
        page: response.page || 1,
        size: response.size || 10
      }
    }
    return {
      data: response?.data || [],
      total: 0,
      page: 1,
      size: 10
    }
  },

  /**
   * 检查是否成功
   * @param {Object} response API响应
   * @returns {boolean}
   */
  isSuccess(response) {
    return response?.code === RESPONSE_CODES.SUCCESS
  },

  /**
   * 获取错误信息
   * @param {Object} response API响应
   * @returns {string}
   */
  getErrorMessage(response) {
    return response?.message || '操作失败'
  }
}

// API接口定义
export const API = {
  // 用户认证
  auth: {
    login: (data) => request({
      url: '/login',
      method: 'POST',
      data
    }),
    refreshToken: (refresh_token) => request({
      url: '/refresh-token',
      method: 'POST',
      data: { refresh_token }
    }),
    logout: () => {
      // 前端退出登录，清除本地存储
      clearAuth()
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      uni.reLaunch({
        url: '/pages/login/login'
      })
      return Promise.resolve()
    },
    getUserInfo: () => request({
      url: '/user/info'
    })
  },
  
  // 二维码扫描
  qr: {
    scan: (qrCode) => request({
      url: `/qr/scan/${qrCode}`
    }),
    scanForReport: (qrCode) => request({
      url: `/qr/scan/report/${qrCode}`
    }),
    scanForRepair: (qrCode) => request({
      url: `/qr/scan/repair/${qrCode}`
    })
  },
  
  // 机器管理
  machines: {
    getList: (params) => request({
      url: '/machines',
      data: params
    }),
    getByCode: (code) => request({
      url: `/machines/code/${code}`
    }),
    getByQRCode: (qrCode) => request({
      url: `/machines/qr/${qrCode}`
    }),
    updateStatus: (id, status) => request({
      url: `/machines/${id}/status`,
      method: 'PUT',
      data: { status }
    })
  },
  
  // 异常管理
  anomalies: {
    create: (data) => request({
      url: '/anomalies/',
      method: 'POST',
      data
    }),
    getList: (params) => request({
      url: '/anomalies/',
      data: params
    }),
    getDetail: (id) => request({
      url: `/anomalies/${id}/`
    }),
    updateStatus: (id, status) => request({
      url: `/anomalies/${id}/status/`,
      method: 'PUT',
      data: { status }
    })
  },
  
  // 维修管理
  repairs: {
    start: (data) => request({
      url: '/repairs/start',
      method: 'POST',
      data
    }),
    complete: (id, data) => request({
      url: `/repairs/${id}/complete`,
      method: 'PUT',
      data
    }),
    getList: (params) => request({
      url: '/repairs',
      data: params
    }),
    getDetail: (id) => request({
      url: `/repairs/${id}`
    })
  }
}

export default API
