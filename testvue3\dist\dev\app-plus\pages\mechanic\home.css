/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mechanic-home {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 1.25rem;
}
.user-header {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 1.25rem 0.9375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.user-info {
  display: flex;
  align-items: center;
}
.avatar {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}
.avatar-text {
  font-size: 1rem;
  font-weight: bold;
  color: #ffffff;
}
.user-details {
  display: flex;
  flex-direction: column;
}
.username {
  font-size: 1rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.25rem;
}
.role {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}
.logout-btn {
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.625rem;
}
.logout-text {
  font-size: 0.75rem;
  color: #ffffff;
}
.scan-tip {
  margin: 0.9375rem;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 0.5rem;
  padding: 0.9375rem;
  display: flex;
  align-items: center;
  box-shadow: 0 0.125rem 0.625rem rgba(255, 152, 0, 0.3);
}
.scan-tip:active {
  transform: scale(0.98);
}
.scan-icon {
  width: 1.875rem;
  height: 1.875rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}
.icon {
  font-size: 0.875rem;
}
.scan-text {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.tip-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.25rem;
}
.tip-desc {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.8);
}
.scan-arrow {
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.arrow {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
}
.stats-overview {
  padding: 0 0.9375rem 0.9375rem;
}
.section-title {
  font-size: 1rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0.75rem;
}
.stats-grid {
  display: flex;
  gap: 0.46875rem;
}
@supports not (gap: 15rpx) {
.stats-grid .stat-card {
    margin-right: 0.625rem;
}
.stats-grid .stat-card:last-child {
    margin-right: 0;
}
}
.stat-card {
  flex: 1;
  background: #ffffff;
  border-radius: 0.375rem;
  padding: 0.75rem 0.5rem;
  text-align: center;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.stat-card.normal {
  border-left: 0.1875rem solid #4CAF50;
}
.stat-card.warning {
  border-left: 0.1875rem solid #FF9800;
}
.stat-card.error {
  border-left: 0.1875rem solid #F44336;
}
.stat-number {
  display: block;
  font-size: 1.125rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0.25rem;
}
.stat-label {
  font-size: 0.6875rem;
  color: #666666;
}
.machine-section {
  padding: 0 0.9375rem;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.refresh-btn {
  font-size: 0.75rem;
  color: #2196F3;
}
.empty-state, .loading-state {
  padding: 1.875rem 0.9375rem;
  text-align: center;
  background: #ffffff;
  border-radius: 0.375rem;
  box-shadow: 0 0.0625rem 0.3125rem rgba(0, 0, 0, 0.05);
}
.empty-text, .loading-text {
  font-size: 0.875rem;
  color: #666666;
  margin-bottom: 0.375rem;
}
.empty-desc {
  font-size: 0.75rem;
  color: #999999;
  line-height: 1.5;
}
.machine-list {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
}
@supports not (gap: 20rpx) {
.machine-list .machine-card {
    margin-bottom: 0.625rem;
}
}
.machine-card {
  background: #ffffff;
  border-radius: 0.5rem;
  padding: 0.9375rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.08);
}
.machine-card:active {
  transform: scale(0.98);
}
.machine-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.625rem;
}
.machine-info {
  display: flex;
  flex-direction: column;
}
.machine-name {
  font-size: 1rem;
  font-weight: bold;
  color: #333333;
  margin-bottom: 0.25rem;
}
.machine-location {
  font-size: 0.75rem;
  color: #666666;
}
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.625rem;
  font-size: 0.6875rem;
}
.status-text {
  font-weight: 500;
}
.machine-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.625rem;
}
.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}
.metric-label {
  font-size: 0.6875rem;
  color: #666666;
  margin-bottom: 0.25rem;
}
.metric-value {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333333;
}
.machine-footer {
  padding: 0.625rem;
  background: #f8f8f8;
  border-radius: 0.375rem;
  margin-bottom: 0.625rem;
}
.error-desc {
  font-size: 0.75rem;
  color: #666666;
  line-height: 1.4;
}
.machine-actions {
  display: flex;
  justify-content: flex-end;
}
.repair-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.625rem;
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
}
.repair-btn:active {
  transform: scale(0.95);
}