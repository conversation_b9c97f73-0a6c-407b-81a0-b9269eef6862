<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { API } from '@/utils/api'

// 机器信息
const machineInfo = ref<any>({
  code: '',
  name: '',
  location: '',
  model: '',
  status: '',
  status_name: ''
})

// 异常信息
const errorInfo = ref<any>({
  id: 0,
  description: '',
  severity: '',
  reportTime: '',
  reporter: '',
  images: []
})

// 维修记录ID
const repairId = ref<number | null>(null)

// 维修表单
const repairForm = reactive({
  process: '',
  parts: '',
  duration: 0, // 维修时长（分钟）
  images: [] as string[]
})

// 维修状态
const repairStatus = ref('not_started') // not_started: 未开始, in_progress: 进行中, completed: 已完成
const isSubmitting = ref(false)
const startTime = ref<Date | null>(null)



// 获取严重程度信息
const getSeverityInfo = (severity: string) => {
  const severityMap = {
    low: { text: '轻微', color: '#4CAF50' },
    medium: { text: '中等', color: '#FF9800' },
    high: { text: '严重', color: '#F44336' },
    critical: { text: '紧急', color: '#9C27B0' }
  }
  return severityMap[severity as keyof typeof severityMap] || { text: '未知', color: '#999999' }
}


// 开始维修
const startRepair = async () => {
  try {
    isSubmitting.value = true
    
    const response = await API.repairs.start({
      anomaly_id: errorInfo.value.id,
      process: repairForm.process || '',
      parts: repairForm.parts || ''
    })
    
    if (response.code === 200) {
      repairId.value = response.data.id
      startTime.value = new Date(response.data.start_time)
      repairStatus.value = 'in_progress'
      
      uni.showToast({
        title: '开始维修',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('开始维修失败:', error)
    uni.showToast({
      title: error.message || '开始维修失败',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}


// 选择图片
const chooseImage = () => {
  uni.chooseImage({
    count: 5,
    sizeType: ['compressed'],
    sourceType: ['camera', 'album'],
    success: (res) => {
      repairForm.images = [...repairForm.images, ...res.tempFilePaths].slice(0, 5)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

// 删除图片
const removeImage = (index: number) => {
  repairForm.images.splice(index, 1)
}

// 预览图片
const previewImage = (index: number) => {
  uni.previewImage({
    urls: repairForm.images,
    current: index
  })
}

// 完成维修
const completeRepair = async () => {
  if (!repairForm.process.trim()) {
    uni.showToast({
      title: '请填写维修过程',
      icon: 'none'
    })
    return
  }

  if (!repairId.value) {
    uni.showToast({
      title: '缺少维修记录ID',
      icon: 'none'
    })
    return
  }

  isSubmitting.value = true
  
  try {
    // 计算维修时长（分钟）
    const duration = startTime.value 
      ? Math.round((Date.now() - startTime.value.getTime()) / (1000 * 60))
      : 0
    
    const response = await API.repairs.complete(repairId.value, {
      process: repairForm.process,
      parts: repairForm.parts || '',
      duration: duration
    })
    
    if (response.code === 200) {
      repairStatus.value = 'completed'
      
      uni.showToast({
        title: '维修完成',
        icon: 'success'
      })
      
      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error('完成维修失败:', error)
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none'
    })
  } finally {
    isSubmitting.value = false
  }
}

// 页面加载时处理参数
onLoad((options: any) => {
  console.log('repair页面接收到的参数:', options)

  try {
    if (options.machineData) {
      // 解析从workspace传递过来的机器数据
      const decodedData = decodeURIComponent(options.machineData)
      const parsedData = JSON.parse(decodedData)
      console.log('解析后的机器数据:', parsedData)

      if (parsedData.machine) {
        machineInfo.value = {
          code: parsedData.machine.code,
          name: parsedData.machine.name,
          location: parsedData.machine.location,
          model: parsedData.machine.model || '未知型号',
          status: parsedData.machine.status,
          status_name: parsedData.machine.status_name,
          qr_code: parsedData.machine.qr_code,
          created_at: parsedData.machine.created_at,
          updated_at: parsedData.machine.updated_at
        }

        // 从传递的数据中获取异常信息
        if (parsedData.anomaly) {
          errorInfo.value = {
            id: parsedData.anomaly.id,
            description: parsedData.anomaly.description || '机器状态异常，需要维修',
            severity: parsedData.anomaly.severity || 'high',
            reportTime: parsedData.anomaly.created_at || parsedData.machine.updated_at,
            reporter: parsedData.anomaly.user?.name || '系统检测',
            images: parsedData.anomaly.images || []
          }
        } else {
          // 兼容旧格式
          errorInfo.value = {
            id: parsedData.machine.id,
            description: parsedData.message || '机器状态异常，需要维修',
            severity: 'high',
            reportTime: parsedData.machine.updated_at,
            reporter: '系统检测',
            images: []
          }
        }
      }
    } else {
      // 如果没有机器信息，返回上一页
      uni.showToast({
        title: '缺少机器信息',
        icon: 'none'
      })
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error('解析机器数据失败:', error)
    uni.showToast({
      title: '数据解析失败',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})
</script>

<template>
  <view class="repair-page">
    <!-- 机器信息卡片 -->
    <view class="machine-card">
      <view class="card-header">
        <text class="card-title">设备信息</text>
        <view class="machine-code">
          <text class="code-text">{{ machineInfo.code }}</text>
        </view>
      </view>
      
      <view class="machine-details">
        <view class="detail-row">
          <text class="detail-label">设备名称：</text>
          <text class="detail-value">{{ machineInfo.name }}</text>
        </view>
        <view class="detail-row">
          <text class="detail-label">设备位置：</text>
          <text class="detail-value">{{ machineInfo.location }}</text>
        </view>
        <view class="detail-row" v-if="machineInfo.created_at">
          <text class="detail-label">安装日期：</text>
          <text class="detail-value">{{ dayjs(machineInfo.created_at).format('YYYY-MM-DD') }}</text>
        </view>
        <view class="detail-row" v-if="machineInfo.updated_at">
          <text class="detail-label">最后维护：</text>
          <text class="detail-value">{{ dayjs(machineInfo.updated_at).format('YYYY-MM-DD') }}</text>
        </view>
      </view>
    </view>
    
    <!-- 异常信息卡片 -->
    <view class="error-card">
      <view class="card-header">
        <text class="card-title">异常信息</text>
        <!-- <view 
          class="severity-badge"
          :style="{ backgroundColor: getSeverityInfo(errorInfo.severity).color }"
        >
          <text class="severity-text">{{ getSeverityInfo(errorInfo.severity).text }}</text>
        </view> -->
      </view>
      
      <view class="error-details">
        <view class="error-desc">
          <text class="desc-text">{{ errorInfo.description }}</text>
        </view>
        <view class="error-meta">
          <text class="meta-item">上报时间：{{ dayjs(errorInfo.reportTime).format('YYYY-MM-DD HH:mm:ss') }}</text>
          <text class="meta-item">上报人：{{ errorInfo.reporter }}</text>
        </view>
      </view>
    </view>
    
    <!-- 维修状态 -->
    <view class="status-card">
      <view class="status-header">
        <text class="status-title">维修状态</text>
        <view 
          class="status-badge"
          :class="repairStatus"
        >
          <text class="status-text">
            {{ repairStatus === 'not_started' ? '未开始' : 
                repairStatus === 'in_progress' ? '进行中' : '已完成' }}
          </text>
        </view>
      </view>
      
      <view class="time-info" v-if="startTime">
        <text class="time-item">开始时间：{{ dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') }}</text>
      </view>
      
      <view class="action-section" v-if="repairStatus === 'not_started'">
        <button 
          class="start-btn" 
          :class="{ 'submitting': isSubmitting }"
          @click="startRepair"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? '开始中...' : '开始维修' }}
        </button>
      </view>
    </view>
    
    <!-- 维修表单 -->
    <view class="form-container" v-if="repairStatus !== 'not_started'">
      <view class="form-section">
        <text class="section-title">维修过程 *</text>
        <textarea 
          class="process-input"
          v-model="repairForm.process"
          placeholder="请详细记录维修过程，包括故障原因分析、维修步骤等"
          placeholder-class="placeholder"
          maxlength="1000"
          show-confirm-bar="false"
          :disabled="repairStatus === 'completed'"
        ></textarea>
        <text class="char-count">{{ repairForm.process.length }}/1000</text>
      </view>
      
      <view class="form-section">
        <text class="section-title">更换配件</text>
        <textarea 
          class="parts-input"
          v-model="repairForm.parts"
          placeholder="记录更换的配件名称、型号、数量等（如无更换可不填）"
          placeholder-class="placeholder"
          maxlength="500"
          show-confirm-bar="false"
          :disabled="repairStatus === 'completed'"
        ></textarea>
        <text class="char-count">{{ repairForm.parts.length }}/500</text>
      </view>
      
      
      
      
    </view>
    
    <!-- 完成按钮 -->
    <view class="submit-section" v-if="repairStatus === 'in_progress'">
      <button 
        class="complete-btn"
        :class="{ 'submitting': isSubmitting }"
        @click="completeRepair"
        :disabled="isSubmitting"
      >
        {{ isSubmitting ? '提交中...' : '完成维修' }}
      </button>
    </view>
  </view>
</template>

<style lang="scss">
.repair-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 30rpx;
  padding-bottom: 120rpx;
}

.machine-card,
.error-card,
.status-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.machine-code {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.code-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.machine-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-row {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 26rpx;
  color: #666666;
  width: 160rpx;
}

.detail-value {
  font-size: 26rpx;
  color: #333333;
  flex: 1;
}

.severity-badge,
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.severity-text,
.status-text {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
}

.status-badge {
  &.not_started {
    background: #999999;
  }
  
  &.in_progress {
    background: #2196F3;
  }
  
  &.completed {
    background: #4CAF50;
  }
}

.error-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.error-desc {
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.desc-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 1.5;
}

.error-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.meta-item {
  font-size: 24rpx;
  color: #666666;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

.time-item {
  font-size: 24rpx;
  color: #666666;
}

.action-section {
  display: flex;
  justify-content: center;
}

.start-btn {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.3);
  
  &:active {
    transform: scale(0.95);
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.form-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.process-input,
.parts-input,
.test-input,
.remarks-input {
  width: 100%;
  min-height: 200rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 26rpx;
  color: #333333;
  background: #fafafa;
  line-height: 1.5;
  
  &:focus {
    border-color: #2196F3;
    background: #ffffff;
  }
  
  &[disabled] {
    background: #f5f5f5;
    color: #999999;
  }
}

.parts-input,
.test-input,
.remarks-input {
  min-height: 120rpx;
}

.placeholder {
  color: #999999;
}

.char-count {
  font-size: 22rpx;
  color: #999999;
  text-align: right;
  margin-top: 12rpx;
}

.result-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background: #fafafa;
}

.result-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.result-text {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 500;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999999;
}

.image-section {
  margin-top: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
}

.image-remove {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

.image-add {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #cccccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  
  &:active {
    background: #f0f0f0;
  }
}

.add-icon {
  font-size: 48rpx;
  color: #cccccc;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 22rpx;
  color: #999999;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.complete-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.3);
  
  &:active {
    transform: translateY(2rpx);
  }
  
  &.submitting {
    background: #cccccc;
    box-shadow: none;
  }
  
  &[disabled] {
    background: #cccccc;
    box-shadow: none;
  }
}
</style>
