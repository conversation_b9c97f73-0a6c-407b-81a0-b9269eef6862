# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
build/
dist/
bin/

# Configuration files with sensitive data
config/production.yml
config/staging.yml

# Air live reload tool
tmp/

# Go module cache
go.sum.bak

# Downloaded Go distributions
go1.*.tar.gz
go/