package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func main() {
	// 测试密码：123456
	password := "123456"
	
	// 生成哈希
	hash, err := HashPassword(password)
	if err != nil {
		fmt.Printf("Error generating hash: %v\n", err)
		return
	}
	
	fmt.Printf("Password: %s\n", password)
	fmt.Printf("Hash: %s\n", hash)
	
	// 验证密码
	isValid := CheckPassword(password, hash)
	fmt.Printf("Password check result: %v\n", isValid)
	
	// 测试错误密码
	wrongPassword := "wrong"
	isValid2 := CheckPassword(wrongPassword, hash)
	fmt.Printf("Wrong password check result: %v\n", isValid2)
}