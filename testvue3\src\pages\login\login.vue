<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { API, setTokens, ResponseHelper } from '@/utils/api'
import { ErrorHandler, ValidationRules } from '@/utils/errorHandler'

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 登录状态
const isLoading = ref(false)

// 密码显示状态
const showPassword = ref(false)

// 记住密码状态
const rememberPassword = ref(false)

// 简单的密码加密/解密函数
const encryptPassword = (password: string): string => {
  // 简单的Base64编码，实际项目中应使用更安全的加密方式
  return btoa(password)
}

const decryptPassword = (encryptedPassword: string): string => {
  try {
    return atob(encryptedPassword)
  } catch (error) {
    return ''
  }
}

// 保存登录信息到本地存储
const saveLoginInfo = () => {
  if (rememberPassword.value) {
    uni.setStorageSync('savedUsername', loginForm.username)
    uni.setStorageSync('savedPassword', encryptPassword(loginForm.password))
    uni.setStorageSync('rememberPassword', true)
  } else {
    // 如果不记住密码，清除保存的信息
    uni.removeStorageSync('savedUsername')
    uni.removeStorageSync('savedPassword')
    uni.removeStorageSync('rememberPassword')
  }
}

// 加载保存的登录信息
const loadSavedLoginInfo = () => {
  const savedRemember = uni.getStorageSync('rememberPassword')
  if (savedRemember) {
    rememberPassword.value = true
    loginForm.username = uni.getStorageSync('savedUsername') || ''
    const savedPassword = uni.getStorageSync('savedPassword')
    if (savedPassword) {
      loginForm.password = decryptPassword(savedPassword)
    }
  }
}

// 清除用户名
const clearUsername = () => {
  loginForm.username = ''
}

// 清除密码
const clearPassword = () => {
  loginForm.password = ''
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 表单验证规则
const validationRules = {
  username: ValidationRules.required('请输入用户名'),
  password: ValidationRules.required('请输入密码')
}

// 登录方法
const handleLogin = async () => {
  // 表单验证
  const validation = ErrorHandler.validateForm(loginForm, validationRules)
  if (!validation.isValid) {
    ErrorHandler.showValidationError(validation)
    return
  }

  // 使用安全执行包装异步操作
  const result = await ErrorHandler.safeExecute(async () => {
    console.log('password',loginForm.password)
    const response = await API.auth.login({
      username: loginForm.username,
      password: loginForm.password
    })
    console.log('response',response)
    // 使用ResponseHelper处理响应
    const loginData = ResponseHelper.getData(response)
    
    if (!loginData || !loginData.access_token || !loginData.refresh_token) {
      throw new Error('登录响应数据异常')
    }
    
    return loginData
  }, {
    loadingRef: isLoading,
    showLoading: false, // 我们用自己的loading状态
    errorHandler: (error) => {
      console.log('error', error)
      ErrorHandler.handleApiError(error, {
        defaultMessage: '登录失败，请检查用户名和密码'
      })
    }
  })

  if (result.success) {
    // 保存双token和用户信息
    setTokens(result.data.access_token, result.data.refresh_token)
    console.log('user',result.data.user)
    uni.setStorageSync('userInfo', result.data.user)

    // 保存登录信息（如果选择了记住密码）
    saveLoginInfo()

    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })

    // 登录成功后跳转到工作台
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/workspace/workspace'
      })
    }, 1500)
  }
}

// 页面加载时初始化
onMounted(() => {
  loadSavedLoginInfo()
})

</script>

<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view class="status-bar"></view>
    
    <!-- 头部logo区域 -->
    <view class="header">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <text class="title">织厂管理系统</text>
      <text class="subtitle">机器异常管理平台</text>
    </view>
    
    <!-- 登录表单 -->
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">用户名</view>
        <view class="input-wrapper">
          <uni-icons 
            type="person" 
            size="20" 
            color="#999999" 
            class="input-prefix-icon"
          ></uni-icons>
          <input
            class="form-input"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            placeholder-class="placeholder"
          />
          <uni-icons
            v-if="loginForm.username"
            type="clear"
            size="18"
            color="#cccccc"
            class="input-suffix-icon clear-icon"
            @click="clearUsername"
          ></uni-icons>
        </view>
      </view>

      <view class="form-item">
        <view class="form-label">密码</view>
        <view class="input-wrapper">
          <uni-icons 
            type="locked" 
            size="20" 
            color="#999999" 
            class="input-prefix-icon"
          ></uni-icons>
          <input
            class="form-input"
            v-model="loginForm.password"
            placeholder="请输入密码"
            placeholder-class="placeholder"
            :password="!showPassword"
          />
          <uni-icons
            v-if="loginForm.password"
            type="clear"
            size="18"
            color="#cccccc"
            class="input-suffix-icon clear-icon"
            @click="clearPassword"
          ></uni-icons>
          <uni-icons
            :type="showPassword ? 'eye-filled' : 'eye-slash-filled'"
            size="20"
            :color="showPassword ? '#2196F3' : '#999999'"
            class="input-suffix-icon eye-icon"
            @click="togglePasswordVisibility"
          ></uni-icons>
        </view>
      </view>

      <!-- 记住密码选项 -->
      <view class="remember-password">
        <view class="checkbox-wrapper" @click="rememberPassword = !rememberPassword">
          <view class="checkbox" :class="{ 'checked': rememberPassword }">
            <uni-icons
              v-if="rememberPassword"
              type="checkmarkempty"
              size="16"
              color="#ffffff"
            ></uni-icons>
          </view>
          <text class="checkbox-label">记住密码</text>
        </view>
      </view>

      <button
        class="login-btn"
        :class="{ 'loading': isLoading }"
        @click="handleLogin"
        :disabled="isLoading"
      >
        {{ isLoading ? '登录中...' : '登录' }}
      </button>
    </view>
    
    <!-- 底部信息 -->
    <view class="footer">
      <text class="footer-text">织厂机器异常管理系统 v1.0.0</text>
    </view>
  </view>
</template>

<style lang="scss">
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
}

.status-bar {
  height: var(--status-bar-height);
}

.header {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 40rpx;
  }
  
  .title {
    font-size: 48rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 20rpx;
  }
  
  .subtitle {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.8);
  }
}

.form-container {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx 0 60rpx; // 左侧留出空间给前缀图标
  font-size: 28rpx;
  color: #333333;
  background: #fafafa;
  box-sizing: border-box;
  
  &:focus {
    border-color: #2196F3;
    background: #ffffff;
  }
}

// 用户名输入框 - 有前缀图标和清除图标
.form-item:first-of-type .form-input {
  padding-right: 70rpx; // 为清除图标留出空间
}

// 密码输入框 - 有前缀图标、清除图标和眼睛图标
.form-item:nth-of-type(2) .form-input {
  padding-right: 120rpx; // 为两个后缀图标留出空间
}

// 前缀图标样式
.input-prefix-icon {
  position: absolute;
  left: 20rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 后缀图标样式
.input-suffix-icon {
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.9);
  }
}

// 用户名输入框的清除图标位置
.form-item:first-of-type .clear-icon {
  right: 20rpx;
}

// 密码输入框的清除图标位置
.form-item:nth-of-type(2) .clear-icon {
  right: 70rpx; // 为眼睛图标留出空间
}

// 眼睛图标位置
.eye-icon {
  right: 20rpx;
}

// 图标悬停效果
.clear-icon:hover {
  opacity: 0.7;
}

.eye-icon:hover {
  opacity: 0.8;
}

.placeholder {
  color: #999999;
}

// 记住密码样式
.remember-password {
  margin: 30rpx 0 20rpx 0;
  display: flex;
  justify-content: flex-start;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;

  &.checked {
    background: #2196F3;
    border-color: #2196F3;
  }

  &:active {
    transform: scale(0.95);
  }
}

.checkbox-label {
  font-size: 26rpx;
  color: #666666;
  line-height: 1;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
  box-shadow: 0 6rpx 20rpx rgba(33, 150, 243, 0.3);
  
  &:active {
    transform: translateY(2rpx);
  }
  
  &.loading {
    background: #cccccc;
    box-shadow: none;
  }
  
  &[disabled] {
    background: #cccccc;
    box-shadow: none;
  }
}

.footer {
  text-align: center;
  padding: 40rpx 0;
}

.footer-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
</style>
