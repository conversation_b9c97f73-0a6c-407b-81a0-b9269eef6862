/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uniui-cart-filled[data-v-946bce22]:before {
  content: "\e6d0";
}
.uniui-gift-filled[data-v-946bce22]:before {
  content: "\e6c4";
}
.uniui-color[data-v-946bce22]:before {
  content: "\e6cf";
}
.uniui-wallet[data-v-946bce22]:before {
  content: "\e6b1";
}
.uniui-settings-filled[data-v-946bce22]:before {
  content: "\e6ce";
}
.uniui-auth-filled[data-v-946bce22]:before {
  content: "\e6cc";
}
.uniui-shop-filled[data-v-946bce22]:before {
  content: "\e6cd";
}
.uniui-staff-filled[data-v-946bce22]:before {
  content: "\e6cb";
}
.uniui-vip-filled[data-v-946bce22]:before {
  content: "\e6c6";
}
.uniui-plus-filled[data-v-946bce22]:before {
  content: "\e6c7";
}
.uniui-folder-add-filled[data-v-946bce22]:before {
  content: "\e6c8";
}
.uniui-color-filled[data-v-946bce22]:before {
  content: "\e6c9";
}
.uniui-tune-filled[data-v-946bce22]:before {
  content: "\e6ca";
}
.uniui-calendar-filled[data-v-946bce22]:before {
  content: "\e6c0";
}
.uniui-notification-filled[data-v-946bce22]:before {
  content: "\e6c1";
}
.uniui-wallet-filled[data-v-946bce22]:before {
  content: "\e6c2";
}
.uniui-medal-filled[data-v-946bce22]:before {
  content: "\e6c3";
}
.uniui-fire-filled[data-v-946bce22]:before {
  content: "\e6c5";
}
.uniui-refreshempty[data-v-946bce22]:before {
  content: "\e6bf";
}
.uniui-location-filled[data-v-946bce22]:before {
  content: "\e6af";
}
.uniui-person-filled[data-v-946bce22]:before {
  content: "\e69d";
}
.uniui-personadd-filled[data-v-946bce22]:before {
  content: "\e698";
}
.uniui-arrowthinleft[data-v-946bce22]:before {
  content: "\e6d2";
}
.uniui-arrowthinup[data-v-946bce22]:before {
  content: "\e6d3";
}
.uniui-arrowthindown[data-v-946bce22]:before {
  content: "\e6d4";
}
.uniui-back[data-v-946bce22]:before {
  content: "\e6b9";
}
.uniui-forward[data-v-946bce22]:before {
  content: "\e6ba";
}
.uniui-arrow-right[data-v-946bce22]:before {
  content: "\e6bb";
}
.uniui-arrow-left[data-v-946bce22]:before {
  content: "\e6bc";
}
.uniui-arrow-up[data-v-946bce22]:before {
  content: "\e6bd";
}
.uniui-arrow-down[data-v-946bce22]:before {
  content: "\e6be";
}
.uniui-arrowthinright[data-v-946bce22]:before {
  content: "\e6d1";
}
.uniui-down[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-bottom[data-v-946bce22]:before {
  content: "\e6b8";
}
.uniui-arrowright[data-v-946bce22]:before {
  content: "\e6d5";
}
.uniui-right[data-v-946bce22]:before {
  content: "\e6b5";
}
.uniui-up[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-top[data-v-946bce22]:before {
  content: "\e6b6";
}
.uniui-left[data-v-946bce22]:before {
  content: "\e6b7";
}
.uniui-arrowup[data-v-946bce22]:before {
  content: "\e6d6";
}
.uniui-eye[data-v-946bce22]:before {
  content: "\e651";
}
.uniui-eye-filled[data-v-946bce22]:before {
  content: "\e66a";
}
.uniui-eye-slash[data-v-946bce22]:before {
  content: "\e6b3";
}
.uniui-eye-slash-filled[data-v-946bce22]:before {
  content: "\e6b4";
}
.uniui-info-filled[data-v-946bce22]:before {
  content: "\e649";
}
.uniui-reload[data-v-946bce22]:before {
  content: "\e6b2";
}
.uniui-micoff-filled[data-v-946bce22]:before {
  content: "\e6b0";
}
.uniui-map-pin-ellipse[data-v-946bce22]:before {
  content: "\e6ac";
}
.uniui-map-pin[data-v-946bce22]:before {
  content: "\e6ad";
}
.uniui-location[data-v-946bce22]:before {
  content: "\e6ae";
}
.uniui-starhalf[data-v-946bce22]:before {
  content: "\e683";
}
.uniui-star[data-v-946bce22]:before {
  content: "\e688";
}
.uniui-star-filled[data-v-946bce22]:before {
  content: "\e68f";
}
.uniui-calendar[data-v-946bce22]:before {
  content: "\e6a0";
}
.uniui-fire[data-v-946bce22]:before {
  content: "\e6a1";
}
.uniui-medal[data-v-946bce22]:before {
  content: "\e6a2";
}
.uniui-font[data-v-946bce22]:before {
  content: "\e6a3";
}
.uniui-gift[data-v-946bce22]:before {
  content: "\e6a4";
}
.uniui-link[data-v-946bce22]:before {
  content: "\e6a5";
}
.uniui-notification[data-v-946bce22]:before {
  content: "\e6a6";
}
.uniui-staff[data-v-946bce22]:before {
  content: "\e6a7";
}
.uniui-vip[data-v-946bce22]:before {
  content: "\e6a8";
}
.uniui-folder-add[data-v-946bce22]:before {
  content: "\e6a9";
}
.uniui-tune[data-v-946bce22]:before {
  content: "\e6aa";
}
.uniui-auth[data-v-946bce22]:before {
  content: "\e6ab";
}
.uniui-person[data-v-946bce22]:before {
  content: "\e699";
}
.uniui-email-filled[data-v-946bce22]:before {
  content: "\e69a";
}
.uniui-phone-filled[data-v-946bce22]:before {
  content: "\e69b";
}
.uniui-phone[data-v-946bce22]:before {
  content: "\e69c";
}
.uniui-email[data-v-946bce22]:before {
  content: "\e69e";
}
.uniui-personadd[data-v-946bce22]:before {
  content: "\e69f";
}
.uniui-chatboxes-filled[data-v-946bce22]:before {
  content: "\e692";
}
.uniui-contact[data-v-946bce22]:before {
  content: "\e693";
}
.uniui-chatbubble-filled[data-v-946bce22]:before {
  content: "\e694";
}
.uniui-contact-filled[data-v-946bce22]:before {
  content: "\e695";
}
.uniui-chatboxes[data-v-946bce22]:before {
  content: "\e696";
}
.uniui-chatbubble[data-v-946bce22]:before {
  content: "\e697";
}
.uniui-upload-filled[data-v-946bce22]:before {
  content: "\e68e";
}
.uniui-upload[data-v-946bce22]:before {
  content: "\e690";
}
.uniui-weixin[data-v-946bce22]:before {
  content: "\e691";
}
.uniui-compose[data-v-946bce22]:before {
  content: "\e67f";
}
.uniui-qq[data-v-946bce22]:before {
  content: "\e680";
}
.uniui-download-filled[data-v-946bce22]:before {
  content: "\e681";
}
.uniui-pyq[data-v-946bce22]:before {
  content: "\e682";
}
.uniui-sound[data-v-946bce22]:before {
  content: "\e684";
}
.uniui-trash-filled[data-v-946bce22]:before {
  content: "\e685";
}
.uniui-sound-filled[data-v-946bce22]:before {
  content: "\e686";
}
.uniui-trash[data-v-946bce22]:before {
  content: "\e687";
}
.uniui-videocam-filled[data-v-946bce22]:before {
  content: "\e689";
}
.uniui-spinner-cycle[data-v-946bce22]:before {
  content: "\e68a";
}
.uniui-weibo[data-v-946bce22]:before {
  content: "\e68b";
}
.uniui-videocam[data-v-946bce22]:before {
  content: "\e68c";
}
.uniui-download[data-v-946bce22]:before {
  content: "\e68d";
}
.uniui-help[data-v-946bce22]:before {
  content: "\e679";
}
.uniui-navigate-filled[data-v-946bce22]:before {
  content: "\e67a";
}
.uniui-plusempty[data-v-946bce22]:before {
  content: "\e67b";
}
.uniui-smallcircle[data-v-946bce22]:before {
  content: "\e67c";
}
.uniui-minus-filled[data-v-946bce22]:before {
  content: "\e67d";
}
.uniui-micoff[data-v-946bce22]:before {
  content: "\e67e";
}
.uniui-closeempty[data-v-946bce22]:before {
  content: "\e66c";
}
.uniui-clear[data-v-946bce22]:before {
  content: "\e66d";
}
.uniui-navigate[data-v-946bce22]:before {
  content: "\e66e";
}
.uniui-minus[data-v-946bce22]:before {
  content: "\e66f";
}
.uniui-image[data-v-946bce22]:before {
  content: "\e670";
}
.uniui-mic[data-v-946bce22]:before {
  content: "\e671";
}
.uniui-paperplane[data-v-946bce22]:before {
  content: "\e672";
}
.uniui-close[data-v-946bce22]:before {
  content: "\e673";
}
.uniui-help-filled[data-v-946bce22]:before {
  content: "\e674";
}
.uniui-paperplane-filled[data-v-946bce22]:before {
  content: "\e675";
}
.uniui-plus[data-v-946bce22]:before {
  content: "\e676";
}
.uniui-mic-filled[data-v-946bce22]:before {
  content: "\e677";
}
.uniui-image-filled[data-v-946bce22]:before {
  content: "\e678";
}
.uniui-locked-filled[data-v-946bce22]:before {
  content: "\e668";
}
.uniui-info[data-v-946bce22]:before {
  content: "\e669";
}
.uniui-locked[data-v-946bce22]:before {
  content: "\e66b";
}
.uniui-camera-filled[data-v-946bce22]:before {
  content: "\e658";
}
.uniui-chat-filled[data-v-946bce22]:before {
  content: "\e659";
}
.uniui-camera[data-v-946bce22]:before {
  content: "\e65a";
}
.uniui-circle[data-v-946bce22]:before {
  content: "\e65b";
}
.uniui-checkmarkempty[data-v-946bce22]:before {
  content: "\e65c";
}
.uniui-chat[data-v-946bce22]:before {
  content: "\e65d";
}
.uniui-circle-filled[data-v-946bce22]:before {
  content: "\e65e";
}
.uniui-flag[data-v-946bce22]:before {
  content: "\e65f";
}
.uniui-flag-filled[data-v-946bce22]:before {
  content: "\e660";
}
.uniui-gear-filled[data-v-946bce22]:before {
  content: "\e661";
}
.uniui-home[data-v-946bce22]:before {
  content: "\e662";
}
.uniui-home-filled[data-v-946bce22]:before {
  content: "\e663";
}
.uniui-gear[data-v-946bce22]:before {
  content: "\e664";
}
.uniui-smallcircle-filled[data-v-946bce22]:before {
  content: "\e665";
}
.uniui-map-filled[data-v-946bce22]:before {
  content: "\e666";
}
.uniui-map[data-v-946bce22]:before {
  content: "\e667";
}
.uniui-refresh-filled[data-v-946bce22]:before {
  content: "\e656";
}
.uniui-refresh[data-v-946bce22]:before {
  content: "\e657";
}
.uniui-cloud-upload[data-v-946bce22]:before {
  content: "\e645";
}
.uniui-cloud-download-filled[data-v-946bce22]:before {
  content: "\e646";
}
.uniui-cloud-download[data-v-946bce22]:before {
  content: "\e647";
}
.uniui-cloud-upload-filled[data-v-946bce22]:before {
  content: "\e648";
}
.uniui-redo[data-v-946bce22]:before {
  content: "\e64a";
}
.uniui-images-filled[data-v-946bce22]:before {
  content: "\e64b";
}
.uniui-undo-filled[data-v-946bce22]:before {
  content: "\e64c";
}
.uniui-more[data-v-946bce22]:before {
  content: "\e64d";
}
.uniui-more-filled[data-v-946bce22]:before {
  content: "\e64e";
}
.uniui-undo[data-v-946bce22]:before {
  content: "\e64f";
}
.uniui-images[data-v-946bce22]:before {
  content: "\e650";
}
.uniui-paperclip[data-v-946bce22]:before {
  content: "\e652";
}
.uniui-settings[data-v-946bce22]:before {
  content: "\e653";
}
.uniui-search[data-v-946bce22]:before {
  content: "\e654";
}
.uniui-redo-filled[data-v-946bce22]:before {
  content: "\e655";
}
.uniui-list[data-v-946bce22]:before {
  content: "\e644";
}
.uniui-mail-open-filled[data-v-946bce22]:before {
  content: "\e63a";
}
.uniui-hand-down-filled[data-v-946bce22]:before {
  content: "\e63c";
}
.uniui-hand-down[data-v-946bce22]:before {
  content: "\e63d";
}
.uniui-hand-up-filled[data-v-946bce22]:before {
  content: "\e63e";
}
.uniui-hand-up[data-v-946bce22]:before {
  content: "\e63f";
}
.uniui-heart-filled[data-v-946bce22]:before {
  content: "\e641";
}
.uniui-mail-open[data-v-946bce22]:before {
  content: "\e643";
}
.uniui-heart[data-v-946bce22]:before {
  content: "\e639";
}
.uniui-loop[data-v-946bce22]:before {
  content: "\e633";
}
.uniui-pulldown[data-v-946bce22]:before {
  content: "\e632";
}
.uniui-scan[data-v-946bce22]:before {
  content: "\e62a";
}
.uniui-bars[data-v-946bce22]:before {
  content: "\e627";
}
.uniui-checkbox[data-v-946bce22]:before {
  content: "\e62b";
}
.uniui-checkbox-filled[data-v-946bce22]:before {
  content: "\e62c";
}
.uniui-shop[data-v-946bce22]:before {
  content: "\e62f";
}
.uniui-headphones[data-v-946bce22]:before {
  content: "\e630";
}
.uniui-cart[data-v-946bce22]:before {
  content: "\e631";
}
@font-face {
  font-family: uniicons;
  src: url("../../assets/uniicons.32e978a5.ttf");
}
.uni-icons[data-v-946bce22] {
  font-family: uniicons;
  text-decoration: none;
  text-align: center;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  padding: 0 1.25rem;
}
.status-bar {
  height: var(--status-bar-height);
}
.header {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 0;
}
.header .logo {
  width: 3.75rem;
  height: 3.75rem;
  margin-bottom: 1.25rem;
}
.header .title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 0.625rem;
}
.header .subtitle {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}
.form-container {
  background: #ffffff;
  border-radius: 0.625rem;
  padding: 1.875rem 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: 0 0.3125rem 0.9375rem rgba(0, 0, 0, 0.1);
}
.form-item {
  margin-bottom: 1.25rem;
}
.form-item:last-child {
  margin-bottom: 0;
}
.form-label {
  font-size: 0.875rem;
  color: #333333;
  margin-bottom: 0.625rem;
  font-weight: 500;
}
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}
.form-input {
  flex: 1;
  height: 2.75rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.375rem;
  padding: 0 0.75rem 0 1.875rem;
  font-size: 0.875rem;
  color: #333333;
  background: #fafafa;
  box-sizing: border-box;
}
.form-input:focus {
  border-color: #2196F3;
  background: #ffffff;
}
.form-item:first-of-type .form-input {
  padding-right: 2.1875rem;
}
.form-item:nth-of-type(2) .form-input {
  padding-right: 3.75rem;
}
.input-prefix-icon {
  position: absolute;
  left: 0.625rem;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
.input-suffix-icon {
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
  transition: all 0.3s ease;
}
.input-suffix-icon:active {
  transform: scale(0.9);
}
.form-item:first-of-type .clear-icon {
  right: 0.625rem;
}
.form-item:nth-of-type(2) .clear-icon {
  right: 2.1875rem;
}
.eye-icon {
  right: 0.625rem;
}
.clear-icon:hover {
  opacity: 0.7;
}
.eye-icon:hover {
  opacity: 0.8;
}
.placeholder {
  color: #999999;
}
.remember-password {
  margin: 0.9375rem 0 0.625rem 0;
  display: flex;
  justify-content: flex-start;
}
.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.checkbox {
  width: 1rem;
  height: 1rem;
  border: 0.0625rem solid #e0e0e0;
  border-radius: 0.1875rem;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  transition: all 0.3s ease;
}
.checkbox.checked {
  background: #2196F3;
  border-color: #2196F3;
}
.checkbox:active {
  transform: scale(0.95);
}
.checkbox-label {
  font-size: 0.8125rem;
  color: #666666;
  line-height: 1;
}
.login-btn {
  width: 100%;
  height: 2.75rem;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: bold;
  margin-top: 1.25rem;
  box-shadow: 0 0.1875rem 0.625rem rgba(33, 150, 243, 0.3);
}
.login-btn:active {
  transform: translateY(0.0625rem);
}
.login-btn.loading {
  background: #cccccc;
  box-shadow: none;
}
.login-btn[disabled] {
  background: #cccccc;
  box-shadow: none;
}
.footer {
  text-align: center;
  padding: 1.25rem 0;
}
.footer-text {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
}