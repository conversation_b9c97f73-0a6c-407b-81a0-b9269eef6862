/**
 * 前端常量定义 - 对应后端枚举值
 */

// 用户角色枚举
export const USER_ROLES = {
  WEAVER: 'weaver',    // 织工
  MECHANIC: 'mechanic' // 机修工
}

// 用户角色ID映射
export const USER_ROLE_IDS = {
  [USER_ROLES.WEAVER]: 1,   // 织工
  [USER_ROLES.MECHANIC]: 2  // 机修工
}

// 用户角色显示名称映射
export const USER_ROLE_NAMES = {
  [USER_ROLES.WEAVER]: '织工',
  [USER_ROLES.MECHANIC]: '机修工'
}

// 根据ID获取角色
export const USER_ROLE_BY_ID = {
  1: USER_ROLES.WEAVER,   // 织工
  2: USER_ROLES.MECHANIC  // 机修工
}

// 机器状态枚举
export const MACHINE_STATUS = {
  NORMAL: 'normal',       // 正常
  ABNORMAL: 'abnormal',   // 异常
  REPAIRING: 'repairing'  // 维修中
}

// 机器状态显示名称映射
export const MACHINE_STATUS_NAMES = {
  [MACHINE_STATUS.NORMAL]: '正常',
  [MACHINE_STATUS.ABNORMAL]: '异常',
  [MACHINE_STATUS.REPAIRING]: '维修中'
}

// 异常严重程度枚举
export const ANOMALY_SEVERITY = {
  LOW: 'low',       // 低
  MEDIUM: 'medium', // 中
  HIGH: 'high'      // 高
}

// 异常严重程度显示名称映射
export const ANOMALY_SEVERITY_NAMES = {
  [ANOMALY_SEVERITY.LOW]: '低',
  [ANOMALY_SEVERITY.MEDIUM]: '中',
  [ANOMALY_SEVERITY.HIGH]: '高'
}

// 异常状态枚举
export const ANOMALY_STATUS = {
  PENDING: 'pending',       // 待维修
  REPAIRING: 'repairing',   // 维修中
  COMPLETED: 'completed'    // 已完成
}

// 异常状态显示名称映射
export const ANOMALY_STATUS_NAMES = {
  [ANOMALY_STATUS.PENDING]: '待维修',
  [ANOMALY_STATUS.REPAIRING]: '维修中',
  [ANOMALY_STATUS.COMPLETED]: '已完成'
}

// 维修状态枚举
export const REPAIR_STATUS = {
  IN_PROGRESS: 'in_progress', // 进行中
  COMPLETED: 'completed'      // 已完成
}

// 维修状态显示名称映射
export const REPAIR_STATUS_NAMES = {
  [REPAIR_STATUS.IN_PROGRESS]: '进行中',
  [REPAIR_STATUS.COMPLETED]: '已完成'
}

// 工具函数：根据枚举值获取显示名称
export const DisplayNameUtils = {
  // 获取用户角色显示名称
  getUserRoleName(role) {
    return USER_ROLE_NAMES[role] || role
  },
  
  // 根据角色ID获取显示名称
  getUserRoleNameById(roleId) {
    const role = USER_ROLE_BY_ID[roleId]
    return role ? USER_ROLE_NAMES[role] : `未知角色(${roleId})`
  },
  
  // 获取机器状态显示名称
  getMachineStatusName(status) {
    return MACHINE_STATUS_NAMES[status] || status
  },
  
  // 获取异常严重程度显示名称
  getAnomalySeverityName(severity) {
    return ANOMALY_SEVERITY_NAMES[severity] || severity
  },
  
  // 获取异常状态显示名称
  getAnomalyStatusName(status) {
    return ANOMALY_STATUS_NAMES[status] || status
  },
  
  // 获取维修状态显示名称
  getRepairStatusName(status) {
    return REPAIR_STATUS_NAMES[status] || status
  }
}

// 数据验证工具
export const ValidationUtils = {
  // 验证用户角色
  isValidUserRole(role) {
    return Object.values(USER_ROLES).includes(role)
  },
  
  // 验证机器状态
  isValidMachineStatus(status) {
    return Object.values(MACHINE_STATUS).includes(status)
  },
  
  // 验证异常严重程度
  isValidAnomalySeverity(severity) {
    return Object.values(ANOMALY_SEVERITY).includes(severity)
  },
  
  // 验证异常状态
  isValidAnomalyStatus(status) {
    return Object.values(ANOMALY_STATUS).includes(status)
  },
  
  // 验证维修状态
  isValidRepairStatus(status) {
    return Object.values(REPAIR_STATUS).includes(status)
  }
}

// 选项数据（用于表单选择）
export const OPTIONS = {
  // 用户角色选项
  userRoles: [
    { value: USER_ROLES.WEAVER, label: USER_ROLE_NAMES[USER_ROLES.WEAVER], id: 1 },
    { value: USER_ROLES.MECHANIC, label: USER_ROLE_NAMES[USER_ROLES.MECHANIC], id: 2 }
  ],
  
  // 机器状态选项
  machineStatus: [
    { value: MACHINE_STATUS.NORMAL, label: MACHINE_STATUS_NAMES[MACHINE_STATUS.NORMAL] },
    { value: MACHINE_STATUS.ABNORMAL, label: MACHINE_STATUS_NAMES[MACHINE_STATUS.ABNORMAL] },
    { value: MACHINE_STATUS.REPAIRING, label: MACHINE_STATUS_NAMES[MACHINE_STATUS.REPAIRING] }
  ],
  
  // 异常严重程度选项
  anomalySeverity: [
    { value: ANOMALY_SEVERITY.LOW, label: ANOMALY_SEVERITY_NAMES[ANOMALY_SEVERITY.LOW] },
    { value: ANOMALY_SEVERITY.MEDIUM, label: ANOMALY_SEVERITY_NAMES[ANOMALY_SEVERITY.MEDIUM] },
    { value: ANOMALY_SEVERITY.HIGH, label: ANOMALY_SEVERITY_NAMES[ANOMALY_SEVERITY.HIGH] }
  ],
  
  // 异常状态选项
  anomalyStatus: [
    { value: ANOMALY_STATUS.PENDING, label: ANOMALY_STATUS_NAMES[ANOMALY_STATUS.PENDING] },
    { value: ANOMALY_STATUS.REPAIRING, label: ANOMALY_STATUS_NAMES[ANOMALY_STATUS.REPAIRING] },
    { value: ANOMALY_STATUS.COMPLETED, label: ANOMALY_STATUS_NAMES[ANOMALY_STATUS.COMPLETED] }
  ],
  
  // 维修状态选项
  repairStatus: [
    { value: REPAIR_STATUS.IN_PROGRESS, label: REPAIR_STATUS_NAMES[REPAIR_STATUS.IN_PROGRESS] },
    { value: REPAIR_STATUS.COMPLETED, label: REPAIR_STATUS_NAMES[REPAIR_STATUS.COMPLETED] }
  ]
}