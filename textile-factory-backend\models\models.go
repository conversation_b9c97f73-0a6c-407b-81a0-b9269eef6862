package models

import (
	"time"
	"textile-factory-backend/constants"
	"gorm.io/gorm"
)

type User struct {
	ID        uint                `json:"id" gorm:"primaryKey"`
	Username  string              `json:"username" gorm:"uniqueIndex;not null"`
	Password  string              `json:"-" gorm:"not null"`
	Role      constants.UserRole  `json:"role" gorm:"not null;type:varchar(20)"`
	RoleID    int                 `json:"role_id" gorm:"not null"`
	RoleName  string              `json:"role_name" gorm:"not null"`
	Name      string              `json:"name" gorm:"not null"`
	CreatedAt time.Time           `json:"created_at"`
	UpdatedAt time.Time           `json:"updated_at"`
}

type Machine struct {
	ID         uint                    `json:"id" gorm:"primaryKey"`
	Code       string                  `json:"code" gorm:"uniqueIndex;not null"`
	Name       string                  `json:"name" gorm:"not null"`
	Location   string                  `json:"location" gorm:"not null"`
	Status     constants.MachineStatus `json:"status" gorm:"default:'normal';type:varchar(20)"`
	StatusName string                  `json:"status_name" gorm:"not null"`
	QRCode     string                  `json:"qr_code" gorm:"not null"`
	CreatedAt  time.Time               `json:"created_at"`
	UpdatedAt  time.Time               `json:"updated_at"`
}

type Anomaly struct {
	ID           uint                     `json:"id" gorm:"primaryKey"`
	MachineID    uint                     `json:"machine_id" gorm:"not null"`
	UserID       uint                     `json:"user_id" gorm:"not null"`
	Description  string                   `json:"description" gorm:"not null"`
	Severity     constants.AnomalySeverity `json:"severity" gorm:"not null;type:varchar(20)"`
	SeverityName string                   `json:"severity_name" gorm:"not null"`
	Status       constants.AnomalyStatus   `json:"status" gorm:"default:'pending';type:varchar(20)"`
	StatusName   string                   `json:"status_name" gorm:"not null"`
	Remark       string                   `json:"remark"`
	CreatedAt    time.Time                `json:"created_at"`
	UpdatedAt    time.Time                `json:"updated_at"`

	Machine Machine `json:"machine" gorm:"foreignKey:MachineID"`
	User    User    `json:"user" gorm:"foreignKey:UserID"`
}

type Repair struct {
	ID         uint                   `json:"id" gorm:"primaryKey"`
	AnomalyID  uint                   `json:"anomaly_id" gorm:"not null;uniqueIndex"`
	MechanicID uint                   `json:"mechanic_id" gorm:"not null"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    *time.Time             `json:"end_time"`
	Process    string                 `json:"process"`
	Parts      string                 `json:"parts"`
	Duration   int                    `json:"duration"`
	Status     constants.RepairStatus `json:"status" gorm:"default:'in_progress';type:varchar(20)"`
	StatusName string                 `json:"status_name" gorm:"not null"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`

	Anomaly  Anomaly `json:"anomaly" gorm:"foreignKey:AnomalyID"`
	Mechanic User    `json:"mechanic" gorm:"foreignKey:MechanicID"`
}

type RefreshToken struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	Token     string    `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	User User `json:"user" gorm:"foreignKey:UserID"`
}

// GORM Hooks - 自动填充显示名称字段
func (u *User) BeforeCreate(tx *gorm.DB) error {
	u.RoleID = u.Role.ID()
	u.RoleName = u.Role.DisplayName()
	return nil
}

func (u *User) BeforeUpdate(tx *gorm.DB) error {
	if tx.Statement.Changed("role") {
		u.RoleID = u.Role.ID()
		u.RoleName = u.Role.DisplayName()
	}
	return nil
}

func (m *Machine) BeforeCreate(tx *gorm.DB) error {
	m.StatusName = m.Status.DisplayName()
	return nil
}

func (m *Machine) BeforeUpdate(tx *gorm.DB) error {
	if tx.Statement.Changed("status") {
		m.StatusName = m.Status.DisplayName()
	}
	return nil
}

func (a *Anomaly) BeforeCreate(tx *gorm.DB) error {
	a.SeverityName = a.Severity.DisplayName()
	a.StatusName = a.Status.DisplayName()
	return nil
}

func (a *Anomaly) BeforeUpdate(tx *gorm.DB) error {
	if tx.Statement.Changed("severity") {
		a.SeverityName = a.Severity.DisplayName()
	}
	if tx.Statement.Changed("status") {
		a.StatusName = a.Status.DisplayName()
	}
	return nil
}

func (r *Repair) BeforeCreate(tx *gorm.DB) error {
	r.StatusName = r.Status.DisplayName()
	return nil
}

func (r *Repair) BeforeUpdate(tx *gorm.DB) error {
	if tx.Statement.Changed("status") {
		r.StatusName = r.Status.DisplayName()
	}
	return nil
}
