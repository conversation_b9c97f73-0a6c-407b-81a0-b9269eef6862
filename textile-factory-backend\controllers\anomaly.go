package controllers

import (
	"strconv"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/constants"
	"textile-factory-backend/response"
	"github.com/gin-gonic/gin"
)

type AnomalyRequest struct {
	MachineID   uint   `json:"machine_id" binding:"required"`
	Description string `json:"description" binding:"required"`
	Severity    string `json:"severity" binding:"required"`
	Remark      string `json:"remark"`
}

func CreateAnomaly(c *gin.Context) {
	var req AnomalyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	userID, _ := c.Get("user_id")
	
	// 验证机器是否存在
	var machine models.Machine
	if err := config.DB.First(&machine, req.MachineID).Error; err != nil {
		response.NotFound(c, "机器不存在")
		return
	}
	
	anomaly := models.Anomaly{
		MachineID:   req.MachineID,
		UserID:      userID.(uint),
		Description: req.Description,
		Severity:    constants.AnomalySeverity(req.Severity),
		Remark:      req.Remark,
		Status:      constants.AnomalyStatusPending,
	}
	
	if err := config.DB.Create(&anomaly).Error; err != nil {
		response.InternalServerError(c, "创建异常记录失败")
		return
	}
	
	// 更新机器状态为异常
	config.DB.Model(&machine).Update("status", constants.MachineStatusAbnormal)
	
	// 返回完整的异常记录
	config.DB.Preload("Machine").Preload("User").First(&anomaly, anomaly.ID)
	
	response.SuccessWithMessage(c, "异常记录创建成功", anomaly)
}

func GetAnomalyList(c *gin.Context) {
	var anomalies []models.Anomaly
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	status := c.Query("status")
	machineID := c.Query("machine_id")
	
	query := config.DB.Model(&models.Anomaly{}).
		Preload("Machine").
		Preload("User")
	
	if status != "" {
		query = query.Where("status = ?", status)
	}
	
	if machineID != "" {
		query = query.Where("machine_id = ?", machineID)
	}
	
	// 织工只能查看自己上报的异常
	role, _ := c.Get("role")
	if role == "织工" {
		userID, _ := c.Get("user_id")
		query = query.Where("user_id = ?", userID)
	}
	
	var total int64
	query.Count(&total)
	
	offset := (page - 1) * pageSize
	result := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&anomalies)
	
	if result.Error != nil {
		response.InternalServerError(c, "查询异常列表失败")
		return
	}
	
	response.Pagination(c, anomalies, total, page, pageSize)
}

func GetAnomalyDetail(c *gin.Context) {
	id := c.Param("id")
	
	var anomaly models.Anomaly
	result := config.DB.Where("id = ?", id).
		Preload("Machine").
		Preload("User").
		First(&anomaly)
	
	if result.Error != nil {
		response.NotFound(c, "异常记录不存在")
		return
	}
	
	// 织工只能查看自己的异常记录
	role, _ := c.Get("role")
	userID, _ := c.Get("user_id")
	if role == "织工" && anomaly.UserID != userID.(uint) {
		response.Forbidden(c, "没有权限查看此记录")
		return
	}
	
	// 获取维修记录
	var repair models.Repair
	config.DB.Where("anomaly_id = ?", anomaly.ID).
		Preload("Mechanic").
		First(&repair)
	
	result_data := gin.H{
		"anomaly": anomaly,
	}
	
	if repair.ID > 0 {
		result_data["repair"] = repair
	}
	
	response.Success(c, result_data)
}

func UpdateAnomalyStatus(c *gin.Context) {
	id := c.Param("id")
	
	var req struct {
		Status string `json:"status" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}
	
	result := config.DB.Model(&models.Anomaly{}).Where("id = ?", id).Update("status", req.Status)
	
	if result.Error != nil {
		response.InternalServerError(c, "更新异常状态失败")
		return
	}
	
	if result.RowsAffected == 0 {
		response.NotFound(c, "异常记录不存在")
		return
	}
	
	response.SuccessWithMessage(c, "异常状态更新成功", nil)
}