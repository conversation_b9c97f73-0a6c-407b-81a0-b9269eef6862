package controllers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"textile-factory-backend/config"
	"textile-factory-backend/models"
	"textile-factory-backend/response"
	"textile-factory-backend/utils"
	"time"
)

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	ExpiresIn    int         `json:"expires_in"`
	User         models.User `json:"user"`
}

func Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	var user models.User
	result := config.DB.Where("username = ?", req.Username).First(&user)
	if result.Error != nil {
		response.Unauthorized(c, "用户名或密码错误")
		return
	}

	// 添加调试信息
	fmt.Printf("Login attempt - Username: %s, Input Password: %s\n", req.Username, req.Password)
	fmt.Printf("Database password hash: %s\n", user.Password)

	// 临时解决方案：支持明文密码比较和bcrypt验证
	passwordValid := false

	// 检查是否是旧的假哈希或明文密码
	if user.Password == "$2a$10$8X8X8X8X8X8X8X8X8X8X8.8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8X8" ||
		user.Password == "123456" {
		fmt.Printf("Detected fake hash or plaintext, comparing plaintext\n")
		passwordValid = (req.Password == "123456")
	} else {
		// 使用bcrypt验证
		passwordValid = utils.CheckPassword(req.Password, user.Password)
		fmt.Printf("Using bcrypt validation, result: %v\n", passwordValid)
	}

	if !passwordValid {
		fmt.Printf("Password validation failed\n")
		response.Unauthorized(c, "用户名或密码错误")
		return
	}

	accessToken, refreshToken, err := utils.GenerateTokenPair(user.ID, user.Username, string(user.Role))
	if err != nil {
		response.InternalServerError(c, "生成token失败")
		return
	}

	// 保存refresh token到数据库
	refreshTokenRecord := models.RefreshToken{
		UserID:    user.ID,
		Token:     refreshToken,
		ExpiresAt: time.Now().Add(7 * 24 * time.Hour), // 7天过期
	}
	if err := config.DB.Create(&refreshTokenRecord).Error; err != nil {
		response.InternalServerError(c, "保存refresh token失败")
		return
	}

	// 清空密码字段
	user.Password = ""

	response.SuccessWithMessage(c, "登录成功", LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    1800, // 30分钟
		User:         user,
	})
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

type RefreshTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

func RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, err.Error())
		return
	}

	// 查找refresh token
	var refreshTokenRecord models.RefreshToken
	result := config.DB.Preload("User").Where("token = ? AND expires_at > ?", req.RefreshToken, time.Now()).First(&refreshTokenRecord)
	if result.Error != nil {
		response.Unauthorized(c, "无效的refresh token")
		return
	}

	// 生成新的access token
	accessToken, err := utils.GenerateAccessToken(refreshTokenRecord.User.ID, refreshTokenRecord.User.Username, string(refreshTokenRecord.User.Role))
	if err != nil {
		response.InternalServerError(c, "生成access token失败")
		return
	}

	response.SuccessWithMessage(c, "刷新token成功", RefreshTokenResponse{
		AccessToken: accessToken,
		ExpiresIn:   1800, // 30分钟
	})
}

func GetUserInfo(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var user models.User
	result := config.DB.First(&user, userID)
	if result.Error != nil {
		response.NotFound(c, "用户不存在")
		return
	}

	// 清空密码字段
	user.Password = ""
	response.Success(c, user)
}
