/**
 * 前端数据类型和常量使用示例
 * 
 * 这个文件展示了如何在各个页面中使用新创建的数据类型和常量
 */

import { 
  DataUtils, 
  User, 
  Machine, 
  Anomaly, 
  Repair 
} from './utils/dataTypes.js'

import { 
  USER_ROLES,
  USER_ROLE_NAMES,
  MACHINE_STATUS,
  MACHINE_STATUS_NAMES,
  ANOMALY_SEVERITY,
  ANOMALY_SEVERITY_NAMES,
  ANOMALY_STATUS,
  ANOMALY_STATUS_NAMES,
  REPAIR_STATUS,
  REPAIR_STATUS_NAMES,
  DisplayNameUtils,
  ValidationUtils,
  OPTIONS
} from './utils/constants.js'

// ==================== 1. 用户数据处理示例 ====================

// 从API获取用户数据并转换
async function handleUserLogin() {
  try {
    const response = await API.auth.login({
      username: 'weaver01',
      password: '123456'
    })
    
    if (response.code === 200) {
      // 转换用户数据
      const user = DataUtils.convertUser(response.data.user)
      
      // 使用用户方法
      console.log('用户名:', user.name)
      console.log('角色显示名称:', user.getRoleDisplayName())
      console.log('是否为织工:', user.isWeaver())
      console.log('是否为机修工:', user.isMechanic())
      
      // 存储用户信息
      uni.setStorageSync('userInfo', user)
    }
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 获取存储的用户信息并验证角色
function getCurrentUser() {
  const userInfo = uni.getStorageSync('userInfo')
  if (userInfo) {
    const user = DataUtils.convertUser(userInfo)
    
    // 根据角色显示不同内容
    if (user.isWeaver()) {
      console.log('当前用户是织工，显示上报功能')
    } else if (user.isMechanic()) {
      console.log('当前用户是机修工，显示维修功能')
    }
    
    return user
  }
  return null
}

// ==================== 2. 机器数据处理示例 ====================

// 获取机器列表并转换
async function getMachineList() {
  try {
    const response = await API.machines.getList()
    
    if (response.code === 200) {
      // 转换机器列表
      const machines = DataUtils.convertMachineList(response.data)
      
      // 使用机器方法
      machines.forEach(machine => {
        console.log(`机器 ${machine.name}:`)
        console.log('  状态:', machine.getStatusDisplayName())
        console.log('  是否正常:', machine.isNormal())
        console.log('  是否异常:', machine.isAbnormal())
        console.log('  是否维修中:', machine.isRepairing())
      })
      
      return machines
    }
  } catch (error) {
    console.error('获取机器列表失败:', error)
  }
}

// 更新机器状态
async function updateMachineStatus(machineId, newStatus) {
  // 验证状态值
  if (!ValidationUtils.isValidMachineStatus(newStatus)) {
    uni.showToast({
      title: '无效的机器状态',
      icon: 'none'
    })
    return
  }
  
  try {
    const response = await API.machines.updateStatus(machineId, newStatus)
    
    if (response.code === 200) {
      uni.showToast({
        title: `状态已更新为: ${DisplayNameUtils.getMachineStatusName(newStatus)}`,
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('更新机器状态失败:', error)
  }
}

// ==================== 3. 异常数据处理示例 ====================

// 创建异常上报
async function createAnomaly(machineId, severity, description) {
  // 验证严重程度
  if (!ValidationUtils.isValidAnomalySeverity(severity)) {
    uni.showToast({
      title: '请选择有效的严重程度',
      icon: 'none'
    })
    return
  }
  
  try {
    const response = await API.anomalies.create({
      machine_id: machineId,
      severity: severity,
      description: description
    })
    
    if (response.code === 200) {
      const anomaly = DataUtils.convertAnomaly(response.data)
      
      uni.showToast({
        title: `异常上报成功，严重程度: ${anomaly.getSeverityDisplayName()}`,
        icon: 'success'
      })
      
      return anomaly
    }
  } catch (error) {
    console.error('创建异常失败:', error)
  }
}

// 获取异常列表并按状态分组
async function getAnomalyListGrouped() {
  try {
    const response = await API.anomalies.getList()
    
    if (response.code === 200) {
      const anomalies = DataUtils.convertAnomalyList(response.data)
      
      // 按状态分组
      const grouped = {
        pending: anomalies.filter(a => a.isPending()),
        repairing: anomalies.filter(a => a.isRepairing()),
        completed: anomalies.filter(a => a.isCompleted())
      }
      
      console.log('待维修异常:', grouped.pending.length)
      console.log('维修中异常:', grouped.repairing.length)
      console.log('已完成异常:', grouped.completed.length)
      
      return grouped
    }
  } catch (error) {
    console.error('获取异常列表失败:', error)
  }
}

// ==================== 4. 维修数据处理示例 ====================

// 开始维修
async function startRepair(anomalyId) {
  try {
    const response = await API.repairs.start({
      anomaly_id: anomalyId
    })
    
    if (response.code === 200) {
      const repair = DataUtils.convertRepair(response.data)
      
      console.log('维修开始:', repair.getStatusDisplayName())
      return repair
    }
  } catch (error) {
    console.error('开始维修失败:', error)
  }
}

// 完成维修
async function completeRepair(repairId, process, parts, duration) {
  try {
    const response = await API.repairs.complete(repairId, {
      process: process,
      parts: parts,
      duration: duration
    })
    
    if (response.code === 200) {
      const repair = DataUtils.convertRepair(response.data)
      
      uni.showToast({
        title: `维修完成，耗时: ${repair.getFormattedDuration()}`,
        icon: 'success'
      })
      
      return repair
    }
  } catch (error) {
    console.error('完成维修失败:', error)
  }
}

// ==================== 5. 在Vue组件中的使用示例 ====================

/*
// 在Vue组件的script部分
import { ref, computed } from 'vue'
import { DataUtils, User } from '@/utils/dataTypes'
import { OPTIONS, DisplayNameUtils } from '@/utils/constants'

export default {
  setup() {
    const userInfo = ref(null)
    const machineList = ref([])
    
    // 计算属性：当前用户角色显示
    const currentUserRole = computed(() => {
      return userInfo.value ? userInfo.value.getRoleDisplayName() : '未知'
    })
    
    // 计算属性：过滤正常状态的机器
    const normalMachines = computed(() => {
      return machineList.value.filter(machine => machine.isNormal())
    })
    
    // 获取用户信息
    const loadUserInfo = () => {
      const info = uni.getStorageSync('userInfo')
      if (info) {
        userInfo.value = DataUtils.convertUser(info)
      }
    }
    
    // 获取机器列表
    const loadMachines = async () => {
      try {
        const response = await API.machines.getList()
        if (response.code === 200) {
          machineList.value = DataUtils.convertMachineList(response.data)
        }
      } catch (error) {
        console.error('加载机器列表失败:', error)
      }
    }
    
    return {
      userInfo,
      machineList,
      currentUserRole,
      normalMachines,
      loadUserInfo,
      loadMachines,
      // 导出常量供模板使用
      OPTIONS
    }
  }
}
*/

// ==================== 6. 表单选项使用示例 ====================

// 在异常上报页面使用严重程度选项
function setupAnomalyForm() {
  return {
    // 严重程度选择器数据
    severityOptions: OPTIONS.anomalySeverity,
    
    // 表单数据
    form: {
      machine_id: 0,
      severity: ANOMALY_SEVERITY.MEDIUM, // 默认中等严重程度
      description: ''
    },
    
    // 提交表单
    async submitForm() {
      if (!ValidationUtils.isValidAnomalySeverity(this.form.severity)) {
        uni.showToast({
          title: '请选择严重程度',
          icon: 'none'
        })
        return
      }
      
      // 提交逻辑...
    }
  }
}

// ==================== 7. 错误处理和验证示例 ====================

// 统一的数据验证函数
function validateFormData(data, type) {
  switch (type) {
    case 'anomaly':
      if (!ValidationUtils.isValidAnomalySeverity(data.severity)) {
        return { valid: false, message: '请选择有效的严重程度' }
      }
      if (!ValidationUtils.isValidAnomalyStatus(data.status)) {
        return { valid: false, message: '无效的异常状态' }
      }
      break
      
    case 'machine':
      if (!ValidationUtils.isValidMachineStatus(data.status)) {
        return { valid: false, message: '无效的机器状态' }
      }
      break
      
    case 'repair':
      if (!ValidationUtils.isValidRepairStatus(data.status)) {
        return { valid: false, message: '无效的维修状态' }
      }
      break
  }
  
  return { valid: true }
}

// 导出示例函数供其他文件参考
export {
  handleUserLogin,
  getCurrentUser,
  getMachineList,
  updateMachineStatus,
  createAnomaly,
  getAnomalyListGrouped,
  startRepair,
  completeRepair,
  setupAnomalyForm,
  validateFormData
}