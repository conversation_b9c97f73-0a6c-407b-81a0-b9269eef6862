package middleware

import (
	"strings"
	"textile-factory-backend/response"
	"textile-factory-backend/utils"
	"textile-factory-backend/constants"
	"github.com/gin-gonic/gin"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>ead<PERSON>("Authorization")
		if authHeader == "" {
			response.Unauthorized(c, "Authorization header required")
			c.Abort()
			return
		}
		
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			response.Unauthorized(c, "Invalid authorization header format")
			c.Abort()
			return
		}
		
		claims, err := utils.ParseToken(parts[1])
		if err != nil {
			response.Unauthorized(c, "Invalid token")
			c.Abort()
			return
		}
		
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		
		c.Next()
	}
}

func RoleMiddleware(allowedRoles ...constants.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			response.Forbidden(c, "Role not found")
			c.Abort()
			return
		}
		
		userRole := constants.UserRole(role.(string))
		for _, allowedRole := range allowedRoles {
			if userRole == allowedRole {
				c.Next()
				return
			}
		}
		
		response.Forbidden(c, "Insufficient permissions")
		c.Abort()
	}
}