<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3111ccbe-2007-4ccd-926d-58e4d0f2c7eb" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="30Lt9fdaEBSh66Qzde2ywUp0ty3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go Build.go build textile-factory-backend.executor": "Debug",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.automatic.dependencies.download": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "801000fc",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "D:/learn/uniapptest/textile-factory-backend",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="go build textile-factory-backend" type="GoApplicationRunConfiguration" factoryName="Go Application" nameIsGenerated="true">
      <module name="textile-factory-backend" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="textile-factory-backend" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-33c477a475b1-b97fc8a1e17c-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-241.14494.238" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-GO-241.14494.238" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>