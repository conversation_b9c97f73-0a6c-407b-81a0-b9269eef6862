package response

import (
	"net/http"
	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`    // 业务状态码
	Message string      `json:"message"` // 消息
	Data    interface{} `json:"data"`    // 数据
}

// PaginationResponse 分页响应结构
type PaginationResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Total   int64       `json:"total"`
	Page    int         `json:"page"`
	Size    int         `json:"size"`
}

// 业务状态码常量
const (
	SUCCESS        = 200  // 成功
	ERROR          = 500  // 通用错误
	INVALID_PARAMS = 400  // 参数错误
	UNAUTHORIZED   = 401  // 未授权
	FORBIDDEN      = 403  // 禁止访问
	NOT_FOUND      = 404  // 资源不存在
)

// 响应消息常量
const (
	SUCCESS_MSG        = "操作成功"
	ERROR_MSG          = "操作失败"
	INVALID_PARAMS_MSG = "参数错误"
	UNAUTHORIZED_MSG   = "未授权"
	FORBIDDEN_MSG      = "禁止访问"
	NOT_FOUND_MSG      = "资源不存在"
)

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: SUCCESS_MSG,
		Data:    data,
	})
}

// SuccessWithMessage 成功响应(自定义消息)
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    SUCCESS,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int, message string) {
	var httpStatus int
	switch code {
	case INVALID_PARAMS:
		httpStatus = http.StatusBadRequest
	case UNAUTHORIZED:
		httpStatus = http.StatusUnauthorized
	case FORBIDDEN:
		httpStatus = http.StatusForbidden
	case NOT_FOUND:
		httpStatus = http.StatusNotFound
	default:
		httpStatus = http.StatusInternalServerError
	}

	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

// ErrorWithData 错误响应(带数据)
func ErrorWithData(c *gin.Context, code int, message string, data interface{}) {
	var httpStatus int
	switch code {
	case INVALID_PARAMS:
		httpStatus = http.StatusBadRequest
	case UNAUTHORIZED:
		httpStatus = http.StatusUnauthorized
	case FORBIDDEN:
		httpStatus = http.StatusForbidden
	case NOT_FOUND:
		httpStatus = http.StatusNotFound
	default:
		httpStatus = http.StatusInternalServerError
	}

	c.JSON(httpStatus, Response{
		Code:    code,
		Message: message,
		Data:    data,
	})
}

// Pagination 分页响应
func Pagination(c *gin.Context, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, PaginationResponse{
		Code:    SUCCESS,
		Message: SUCCESS_MSG,
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// PaginationWithMessage 分页响应(自定义消息)
func PaginationWithMessage(c *gin.Context, message string, data interface{}, total int64, page, size int) {
	c.JSON(http.StatusOK, PaginationResponse{
		Code:    SUCCESS,
		Message: message,
		Data:    data,
		Total:   total,
		Page:    page,
		Size:    size,
	})
}

// 快捷方法
func BadRequest(c *gin.Context, message string) {
	Error(c, INVALID_PARAMS, message)
}

func Unauthorized(c *gin.Context, message string) {
	Error(c, UNAUTHORIZED, message)
}

func Forbidden(c *gin.Context, message string) {
	Error(c, FORBIDDEN, message)
}

func NotFound(c *gin.Context, message string) {
	Error(c, NOT_FOUND, message)
}

func InternalServerError(c *gin.Context, message string) {
	Error(c, ERROR, message)
}