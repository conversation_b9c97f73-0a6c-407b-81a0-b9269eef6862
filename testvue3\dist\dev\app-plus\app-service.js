var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global2 = uni.requireGlobal();
  ArrayBuffer = global2.ArrayBuffer;
  Int8Array = global2.Int8Array;
  Uint8Array = global2.Uint8Array;
  Uint8ClampedArray = global2.Uint8ClampedArray;
  Int16Array = global2.Int16Array;
  Uint16Array = global2.Uint16Array;
  Int32Array = global2.Int32Array;
  Uint32Array = global2.Uint32Array;
  Float32Array = global2.Float32Array;
  Float64Array = global2.Float64Array;
  BigInt64Array = global2.BigInt64Array;
  BigUint64Array = global2.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function _mergeNamespaces(n, m) {
    for (var i = 0; i < m.length; i++) {
      const e = m[i];
      if (typeof e !== "string" && !Array.isArray(e)) {
        for (const k in e) {
          if (k !== "default" && !(k in n)) {
            const d = Object.getOwnPropertyDescriptor(e, k);
            if (d) {
              Object.defineProperty(n, k, d.get ? d : {
                enumerable: true,
                get: () => e[k]
              });
            }
          }
        }
      }
    }
    return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: "Module" }));
  }
  const fontData = [
    {
      "font_class": "arrow-down",
      "unicode": ""
    },
    {
      "font_class": "arrow-left",
      "unicode": ""
    },
    {
      "font_class": "arrow-right",
      "unicode": ""
    },
    {
      "font_class": "arrow-up",
      "unicode": ""
    },
    {
      "font_class": "auth",
      "unicode": ""
    },
    {
      "font_class": "auth-filled",
      "unicode": ""
    },
    {
      "font_class": "back",
      "unicode": ""
    },
    {
      "font_class": "bars",
      "unicode": ""
    },
    {
      "font_class": "calendar",
      "unicode": ""
    },
    {
      "font_class": "calendar-filled",
      "unicode": ""
    },
    {
      "font_class": "camera",
      "unicode": ""
    },
    {
      "font_class": "camera-filled",
      "unicode": ""
    },
    {
      "font_class": "cart",
      "unicode": ""
    },
    {
      "font_class": "cart-filled",
      "unicode": ""
    },
    {
      "font_class": "chat",
      "unicode": ""
    },
    {
      "font_class": "chat-filled",
      "unicode": ""
    },
    {
      "font_class": "chatboxes",
      "unicode": ""
    },
    {
      "font_class": "chatboxes-filled",
      "unicode": ""
    },
    {
      "font_class": "chatbubble",
      "unicode": ""
    },
    {
      "font_class": "chatbubble-filled",
      "unicode": ""
    },
    {
      "font_class": "checkbox",
      "unicode": ""
    },
    {
      "font_class": "checkbox-filled",
      "unicode": ""
    },
    {
      "font_class": "checkmarkempty",
      "unicode": ""
    },
    {
      "font_class": "circle",
      "unicode": ""
    },
    {
      "font_class": "circle-filled",
      "unicode": ""
    },
    {
      "font_class": "clear",
      "unicode": ""
    },
    {
      "font_class": "close",
      "unicode": ""
    },
    {
      "font_class": "closeempty",
      "unicode": ""
    },
    {
      "font_class": "cloud-download",
      "unicode": ""
    },
    {
      "font_class": "cloud-download-filled",
      "unicode": ""
    },
    {
      "font_class": "cloud-upload",
      "unicode": ""
    },
    {
      "font_class": "cloud-upload-filled",
      "unicode": ""
    },
    {
      "font_class": "color",
      "unicode": ""
    },
    {
      "font_class": "color-filled",
      "unicode": ""
    },
    {
      "font_class": "compose",
      "unicode": ""
    },
    {
      "font_class": "contact",
      "unicode": ""
    },
    {
      "font_class": "contact-filled",
      "unicode": ""
    },
    {
      "font_class": "down",
      "unicode": ""
    },
    {
      "font_class": "bottom",
      "unicode": ""
    },
    {
      "font_class": "download",
      "unicode": ""
    },
    {
      "font_class": "download-filled",
      "unicode": ""
    },
    {
      "font_class": "email",
      "unicode": ""
    },
    {
      "font_class": "email-filled",
      "unicode": ""
    },
    {
      "font_class": "eye",
      "unicode": ""
    },
    {
      "font_class": "eye-filled",
      "unicode": ""
    },
    {
      "font_class": "eye-slash",
      "unicode": ""
    },
    {
      "font_class": "eye-slash-filled",
      "unicode": ""
    },
    {
      "font_class": "fire",
      "unicode": ""
    },
    {
      "font_class": "fire-filled",
      "unicode": ""
    },
    {
      "font_class": "flag",
      "unicode": ""
    },
    {
      "font_class": "flag-filled",
      "unicode": ""
    },
    {
      "font_class": "folder-add",
      "unicode": ""
    },
    {
      "font_class": "folder-add-filled",
      "unicode": ""
    },
    {
      "font_class": "font",
      "unicode": ""
    },
    {
      "font_class": "forward",
      "unicode": ""
    },
    {
      "font_class": "gear",
      "unicode": ""
    },
    {
      "font_class": "gear-filled",
      "unicode": ""
    },
    {
      "font_class": "gift",
      "unicode": ""
    },
    {
      "font_class": "gift-filled",
      "unicode": ""
    },
    {
      "font_class": "hand-down",
      "unicode": ""
    },
    {
      "font_class": "hand-down-filled",
      "unicode": ""
    },
    {
      "font_class": "hand-up",
      "unicode": ""
    },
    {
      "font_class": "hand-up-filled",
      "unicode": ""
    },
    {
      "font_class": "headphones",
      "unicode": ""
    },
    {
      "font_class": "heart",
      "unicode": ""
    },
    {
      "font_class": "heart-filled",
      "unicode": ""
    },
    {
      "font_class": "help",
      "unicode": ""
    },
    {
      "font_class": "help-filled",
      "unicode": ""
    },
    {
      "font_class": "home",
      "unicode": ""
    },
    {
      "font_class": "home-filled",
      "unicode": ""
    },
    {
      "font_class": "image",
      "unicode": ""
    },
    {
      "font_class": "image-filled",
      "unicode": ""
    },
    {
      "font_class": "images",
      "unicode": ""
    },
    {
      "font_class": "images-filled",
      "unicode": ""
    },
    {
      "font_class": "info",
      "unicode": ""
    },
    {
      "font_class": "info-filled",
      "unicode": ""
    },
    {
      "font_class": "left",
      "unicode": ""
    },
    {
      "font_class": "link",
      "unicode": ""
    },
    {
      "font_class": "list",
      "unicode": ""
    },
    {
      "font_class": "location",
      "unicode": ""
    },
    {
      "font_class": "location-filled",
      "unicode": ""
    },
    {
      "font_class": "locked",
      "unicode": ""
    },
    {
      "font_class": "locked-filled",
      "unicode": ""
    },
    {
      "font_class": "loop",
      "unicode": ""
    },
    {
      "font_class": "mail-open",
      "unicode": ""
    },
    {
      "font_class": "mail-open-filled",
      "unicode": ""
    },
    {
      "font_class": "map",
      "unicode": ""
    },
    {
      "font_class": "map-filled",
      "unicode": ""
    },
    {
      "font_class": "map-pin",
      "unicode": ""
    },
    {
      "font_class": "map-pin-ellipse",
      "unicode": ""
    },
    {
      "font_class": "medal",
      "unicode": ""
    },
    {
      "font_class": "medal-filled",
      "unicode": ""
    },
    {
      "font_class": "mic",
      "unicode": ""
    },
    {
      "font_class": "mic-filled",
      "unicode": ""
    },
    {
      "font_class": "micoff",
      "unicode": ""
    },
    {
      "font_class": "micoff-filled",
      "unicode": ""
    },
    {
      "font_class": "minus",
      "unicode": ""
    },
    {
      "font_class": "minus-filled",
      "unicode": ""
    },
    {
      "font_class": "more",
      "unicode": ""
    },
    {
      "font_class": "more-filled",
      "unicode": ""
    },
    {
      "font_class": "navigate",
      "unicode": ""
    },
    {
      "font_class": "navigate-filled",
      "unicode": ""
    },
    {
      "font_class": "notification",
      "unicode": ""
    },
    {
      "font_class": "notification-filled",
      "unicode": ""
    },
    {
      "font_class": "paperclip",
      "unicode": ""
    },
    {
      "font_class": "paperplane",
      "unicode": ""
    },
    {
      "font_class": "paperplane-filled",
      "unicode": ""
    },
    {
      "font_class": "person",
      "unicode": ""
    },
    {
      "font_class": "person-filled",
      "unicode": ""
    },
    {
      "font_class": "personadd",
      "unicode": ""
    },
    {
      "font_class": "personadd-filled",
      "unicode": ""
    },
    {
      "font_class": "personadd-filled-copy",
      "unicode": ""
    },
    {
      "font_class": "phone",
      "unicode": ""
    },
    {
      "font_class": "phone-filled",
      "unicode": ""
    },
    {
      "font_class": "plus",
      "unicode": ""
    },
    {
      "font_class": "plus-filled",
      "unicode": ""
    },
    {
      "font_class": "plusempty",
      "unicode": ""
    },
    {
      "font_class": "pulldown",
      "unicode": ""
    },
    {
      "font_class": "pyq",
      "unicode": ""
    },
    {
      "font_class": "qq",
      "unicode": ""
    },
    {
      "font_class": "redo",
      "unicode": ""
    },
    {
      "font_class": "redo-filled",
      "unicode": ""
    },
    {
      "font_class": "refresh",
      "unicode": ""
    },
    {
      "font_class": "refresh-filled",
      "unicode": ""
    },
    {
      "font_class": "refreshempty",
      "unicode": ""
    },
    {
      "font_class": "reload",
      "unicode": ""
    },
    {
      "font_class": "right",
      "unicode": ""
    },
    {
      "font_class": "scan",
      "unicode": ""
    },
    {
      "font_class": "search",
      "unicode": ""
    },
    {
      "font_class": "settings",
      "unicode": ""
    },
    {
      "font_class": "settings-filled",
      "unicode": ""
    },
    {
      "font_class": "shop",
      "unicode": ""
    },
    {
      "font_class": "shop-filled",
      "unicode": ""
    },
    {
      "font_class": "smallcircle",
      "unicode": ""
    },
    {
      "font_class": "smallcircle-filled",
      "unicode": ""
    },
    {
      "font_class": "sound",
      "unicode": ""
    },
    {
      "font_class": "sound-filled",
      "unicode": ""
    },
    {
      "font_class": "spinner-cycle",
      "unicode": ""
    },
    {
      "font_class": "staff",
      "unicode": ""
    },
    {
      "font_class": "staff-filled",
      "unicode": ""
    },
    {
      "font_class": "star",
      "unicode": ""
    },
    {
      "font_class": "star-filled",
      "unicode": ""
    },
    {
      "font_class": "starhalf",
      "unicode": ""
    },
    {
      "font_class": "trash",
      "unicode": ""
    },
    {
      "font_class": "trash-filled",
      "unicode": ""
    },
    {
      "font_class": "tune",
      "unicode": ""
    },
    {
      "font_class": "tune-filled",
      "unicode": ""
    },
    {
      "font_class": "undo",
      "unicode": ""
    },
    {
      "font_class": "undo-filled",
      "unicode": ""
    },
    {
      "font_class": "up",
      "unicode": ""
    },
    {
      "font_class": "top",
      "unicode": ""
    },
    {
      "font_class": "upload",
      "unicode": ""
    },
    {
      "font_class": "upload-filled",
      "unicode": ""
    },
    {
      "font_class": "videocam",
      "unicode": ""
    },
    {
      "font_class": "videocam-filled",
      "unicode": ""
    },
    {
      "font_class": "vip",
      "unicode": ""
    },
    {
      "font_class": "vip-filled",
      "unicode": ""
    },
    {
      "font_class": "wallet",
      "unicode": ""
    },
    {
      "font_class": "wallet-filled",
      "unicode": ""
    },
    {
      "font_class": "weibo",
      "unicode": ""
    },
    {
      "font_class": "weixin",
      "unicode": ""
    }
  ];
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const getVal = (val) => {
    const reg = /^[0-9]*$/g;
    return typeof val === "number" || reg.test(val) ? val + "px" : val;
  };
  const _sfc_main$9 = {
    name: "UniIcons",
    emits: ["click"],
    props: {
      type: {
        type: String,
        default: ""
      },
      color: {
        type: String,
        default: "#333333"
      },
      size: {
        type: [Number, String],
        default: 16
      },
      customPrefix: {
        type: String,
        default: ""
      },
      fontFamily: {
        type: String,
        default: ""
      }
    },
    data() {
      return {
        icons: fontData
      };
    },
    computed: {
      unicode() {
        let code = this.icons.find((v) => v.font_class === this.type);
        if (code) {
          return code.unicode;
        }
        return "";
      },
      iconSize() {
        return getVal(this.size);
      },
      styleObj() {
        if (this.fontFamily !== "") {
          return `color: ${this.color}; font-size: ${this.iconSize}; font-family: ${this.fontFamily};`;
        }
        return `color: ${this.color}; font-size: ${this.iconSize};`;
      }
    },
    methods: {
      _onClick() {
        this.$emit("click");
      }
    }
  };
  function _sfc_render$8(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock(
      "text",
      {
        style: vue.normalizeStyle($options.styleObj),
        class: vue.normalizeClass(["uni-icons", ["uniui-" + $props.type, $props.customPrefix, $props.customPrefix ? $props.type : ""]]),
        onClick: _cache[0] || (_cache[0] = (...args) => $options._onClick && $options._onClick(...args))
      },
      [
        vue.renderSlot(_ctx.$slots, "default", {}, void 0, true)
      ],
      6
      /* CLASS, STYLE */
    );
  }
  const __easycom_0 = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render$8], ["__scopeId", "data-v-946bce22"], ["__file", "D:/learn/uniapptest/testvue3/node_modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue"]]);
  const ON_LOAD = "onLoad";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  function resolveEasycom(component, easycom) {
    return typeof component === "string" ? easycom : component;
  }
  const createHook = (lifecycle) => (hook, target = vue.getCurrentInstance()) => {
    !vue.isInSSRComponentSetup && vue.injectHook(lifecycle, hook, target);
  };
  const onLoad = /* @__PURE__ */ createHook(ON_LOAD);
  const API_CONFIG = {
    TIMEOUT: 1e4
  };
  const RESPONSE_CODES = {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  };
  function getBaseUrl() {
    return "http://192.168.1.58:8080/api/v1";
  }
  let accessToken = uni.getStorageSync("access_token") || "";
  let refreshToken = uni.getStorageSync("refresh_token") || "";
  function setTokens(access_token, refresh_token) {
    accessToken = access_token;
    refreshToken = refresh_token;
    uni.setStorageSync("access_token", access_token);
    uni.setStorageSync("refresh_token", refresh_token);
  }
  function getAccessToken() {
    return accessToken || uni.getStorageSync("access_token");
  }
  function getRefreshToken() {
    return refreshToken || uni.getStorageSync("refresh_token");
  }
  function getAuthToken() {
    return getAccessToken();
  }
  function clearAuth() {
    accessToken = "";
    refreshToken = "";
    uni.removeStorageSync("access_token");
    uni.removeStorageSync("refresh_token");
    uni.removeStorageSync("token");
    uni.removeStorageSync("userInfo");
  }
  async function refreshAccessToken() {
    const refresh_token = getRefreshToken();
    if (!refresh_token) {
      throw new Error("No refresh token available");
    }
    try {
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: `${getBaseUrl()}/refresh-token`,
          method: "POST",
          data: { refresh_token },
          header: { "Content-Type": "application/json" },
          success: resolve,
          fail: reject
        });
      });
      if (response.statusCode === 200 && response.data.code === RESPONSE_CODES.SUCCESS) {
        const newAccessToken = response.data.data.access_token;
        setTokens(newAccessToken, refresh_token);
        return newAccessToken;
      } else {
        throw new Error("Refresh token failed");
      }
    } catch (error) {
      formatAppLog("error", "at utils/api.js:119", "刷新token失败:", error);
      clearAuth();
      throw error;
    }
  }
  function normalizeUrl(baseUrl, path) {
    const base = baseUrl.replace(/\/$/, "");
    const normalizedPath = path.startsWith("/") ? path : "/" + path;
    return base + normalizedPath;
  }
  function request(options) {
    return new Promise(async (resolve, reject) => {
      const token = getAuthToken();
      const baseUrl = getBaseUrl();
      formatAppLog("log", "at utils/api.js:144", "发起API请求:", {
        url: `${baseUrl}${options.url}`,
        method: options.method || "GET",
        data: options.data,
        hasToken: !!token
      });
      uni.request({
        url: normalizeUrl(baseUrl, options.url),
        method: options.method || "GET",
        data: options.data || {},
        header: {
          "Content-Type": "application/json",
          ...token ? { "Authorization": `Bearer ${token}` } : {},
          ...options.header || {}
        },
        timeout: API_CONFIG.TIMEOUT,
        success: async (res) => {
          formatAppLog("log", "at utils/api.js:162", "API请求成功:", res);
          if (res.statusCode === 200) {
            const response = res.data;
            if (response && typeof response.code !== "undefined") {
              if (response.code === RESPONSE_CODES.SUCCESS) {
                resolve(response);
              } else {
                const errorMsg = response.message || "操作失败";
                reject(new Error(errorMsg));
              }
            } else {
              resolve(response);
            }
          } else if (res.statusCode === 401) {
            formatAppLog("log", "at utils/api.js:184", "res", res);
            try {
              if (getRefreshToken()) {
                formatAppLog("log", "at utils/api.js:188", "尝试刷新access token");
                await refreshAccessToken();
                const newToken = getAccessToken();
                const retryOptions = {
                  ...options,
                  header: {
                    ...options.header,
                    "Authorization": `Bearer ${newToken}`
                  }
                };
                if (!options._isRetry) {
                  retryOptions._isRetry = true;
                  const retryResult = await request(retryOptions);
                  resolve(retryResult);
                  return;
                }
              }
              throw new Error("Token refresh failed");
            } catch (refreshError) {
              formatAppLog("error", "at utils/api.js:209", "Token刷新失败:", refreshError);
              clearAuth();
              uni.showToast({
                title: "登录已过期，请重新登录",
                icon: "none"
              });
              uni.reLaunch({
                url: "/pages/login/login"
              });
              reject(new Error(res.data.error || "登录已过期"));
            }
          } else {
            const response = res.data;
            let errorMsg = `请求失败 (${res.statusCode})`;
            if (response) {
              if (response.message) {
                errorMsg = response.message;
              } else if (response.error) {
                errorMsg = response.error;
              }
            }
            reject(new Error(errorMsg));
          }
        },
        fail: (err) => {
          formatAppLog("error", "at utils/api.js:239", "API请求失败:", err);
          let errorMsg = "网络请求失败";
          if (err.errMsg) {
            if (err.errMsg.includes("timeout")) {
              errorMsg = "请求超时，请检查网络连接";
            } else if (err.errMsg.includes("fail")) {
              if (err.errMsg.includes("localhost") || err.errMsg.includes("127.0.0.1")) {
                errorMsg = "无法连接到本地服务器，请确保后端服务已启动";
              } else {
                errorMsg = "无法连接到服务器，请检查网络和服务器状态";
              }
            } else if (err.errMsg.includes("abort")) {
              errorMsg = "请求被中断，请检查网络连接";
            }
          }
          reject(new Error(errorMsg));
        }
      });
    });
  }
  const ResponseHelper = {
    /**
     * 获取响应数据
     * @param {Object} response API响应
     * @returns {any} 数据
     */
    getData(response) {
      return (response == null ? void 0 : response.data) || null;
    },
    /**
     * 获取分页数据
     * @param {Object} response API响应
     * @returns {Object} 分页信息
     */
    getPagination(response) {
      if ((response == null ? void 0 : response.total) !== void 0) {
        return {
          data: response.data || [],
          total: response.total || 0,
          page: response.page || 1,
          size: response.size || 10
        };
      }
      return {
        data: (response == null ? void 0 : response.data) || [],
        total: 0,
        page: 1,
        size: 10
      };
    },
    /**
     * 检查是否成功
     * @param {Object} response API响应
     * @returns {boolean}
     */
    isSuccess(response) {
      return (response == null ? void 0 : response.code) === RESPONSE_CODES.SUCCESS;
    },
    /**
     * 获取错误信息
     * @param {Object} response API响应
     * @returns {string}
     */
    getErrorMessage(response) {
      return (response == null ? void 0 : response.message) || "操作失败";
    }
  };
  const API = {
    // 用户认证
    auth: {
      login: (data) => request({
        url: "/login",
        method: "POST",
        data
      }),
      refreshToken: (refresh_token) => request({
        url: "/refresh-token",
        method: "POST",
        data: { refresh_token }
      }),
      logout: () => {
        clearAuth();
        uni.showToast({
          title: "已退出登录",
          icon: "success"
        });
        uni.reLaunch({
          url: "/pages/login/login"
        });
        return Promise.resolve();
      },
      getUserInfo: () => request({
        url: "/user/info"
      })
    },
    // 二维码扫描
    qr: {
      scan: (qrCode) => request({
        url: `/qr/scan/${qrCode}`
      }),
      scanForReport: (qrCode) => request({
        url: `/qr/scan/report/${qrCode}`
      }),
      scanForRepair: (qrCode) => request({
        url: `/qr/scan/repair/${qrCode}`
      })
    },
    // 机器管理
    machines: {
      getList: (params) => request({
        url: "/machines",
        data: params
      }),
      getByCode: (code) => request({
        url: `/machines/code/${code}`
      }),
      getByQRCode: (qrCode) => request({
        url: `/machines/qr/${qrCode}`
      }),
      updateStatus: (id, status) => request({
        url: `/machines/${id}/status`,
        method: "PUT",
        data: { status }
      })
    },
    // 异常管理
    anomalies: {
      create: (data) => request({
        url: "/anomalies/",
        method: "POST",
        data
      }),
      getList: (params) => request({
        url: "/anomalies/",
        data: params
      }),
      getDetail: (id) => request({
        url: `/anomalies/${id}/`
      }),
      updateStatus: (id, status) => request({
        url: `/anomalies/${id}/status/`,
        method: "PUT",
        data: { status }
      })
    },
    // 维修管理
    repairs: {
      start: (data) => request({
        url: "/repairs/start",
        method: "POST",
        data
      }),
      complete: (id, data) => request({
        url: `/repairs/${id}/complete`,
        method: "PUT",
        data
      }),
      getList: (params) => request({
        url: "/repairs",
        data: params
      }),
      getDetail: (id) => request({
        url: `/repairs/${id}`
      })
    }
  };
  class ErrorHandler {
    /**
     * 处理API错误
     * @param {Error} error 错误对象
     * @param {Object} options 选项
     * @returns {string} 错误消息
     */
    static handleApiError(error, options = {}) {
      const {
        showToast = true,
        defaultMessage = "操作失败",
        duration = 2e3
      } = options;
      let message = (error == null ? void 0 : error.message) || defaultMessage;
      if (error == null ? void 0 : error.message) {
        if (error.message.includes("网络")) {
          message = "网络连接失败，请检查网络后重试";
        } else if (error.message.includes("超时")) {
          message = "请求超时，请重试";
        } else if (error.message.includes("登录已过期")) {
          message = "登录已过期，请重新登录";
        }
      }
      if (showToast) {
        uni.showToast({
          title: message,
          icon: "none",
          duration
        });
      }
      return message;
    }
    /**
     * 处理表单验证错误
     * @param {Object} formData 表单数据
     * @param {Object} rules 验证规则
     * @returns {Object} 验证结果
     */
    static validateForm(formData, rules) {
      const errors = {};
      let isValid = true;
      for (const field in rules) {
        const rule = rules[field];
        const value = formData[field];
        if (rule.required && (!value || typeof value === "string" && !value.trim())) {
          errors[field] = rule.message || `${field}不能为空`;
          isValid = false;
          continue;
        }
        if (value && rule.pattern && !rule.pattern.test(value)) {
          errors[field] = rule.message || `${field}格式不正确`;
          isValid = false;
          continue;
        }
        if (value && rule.minLength && value.length < rule.minLength) {
          errors[field] = rule.message || `${field}长度不能少于${rule.minLength}个字符`;
          isValid = false;
          continue;
        }
        if (value && rule.maxLength && value.length > rule.maxLength) {
          errors[field] = rule.message || `${field}长度不能超过${rule.maxLength}个字符`;
          isValid = false;
          continue;
        }
      }
      return {
        isValid,
        errors,
        firstError: isValid ? null : Object.values(errors)[0]
      };
    }
    /**
     * 显示表单验证错误
     * @param {Object} validation 验证结果
     */
    static showValidationError(validation) {
      if (!validation.isValid && validation.firstError) {
        uni.showToast({
          title: validation.firstError,
          icon: "none",
          duration: 2e3
        });
      }
    }
    /**
     * 安全执行异步操作
     * @param {Function} asyncFn 异步函数
     * @param {Object} options 选项
     * @returns {Promise} 执行结果
     */
    static async safeExecute(asyncFn, options = {}) {
      const {
        loadingRef = null,
        errorHandler = this.handleApiError,
        showLoading = false,
        loadingText = "处理中..."
      } = options;
      try {
        if (loadingRef) {
          loadingRef.value = true;
        }
        if (showLoading) {
          uni.showLoading({
            title: loadingText,
            mask: true
          });
        }
        const result = await asyncFn();
        if (showLoading) {
          uni.hideLoading();
        }
        return {
          success: true,
          data: result,
          error: null
        };
      } catch (error) {
        formatAppLog("error", "at utils/errorHandler.js:146", "SafeExecute error:", error);
        if (showLoading) {
          uni.hideLoading();
        }
        if (errorHandler) {
          errorHandler(error);
        }
        return {
          success: false,
          data: null,
          error
        };
      } finally {
        if (loadingRef) {
          loadingRef.value = false;
        }
      }
    }
  }
  const ValidationRules = {
    required: (message) => ({
      required: true,
      message
    }),
    minLength: (length, message) => ({
      minLength: length,
      message
    }),
    maxLength: (length, message) => ({
      maxLength: length,
      message
    }),
    pattern: (pattern, message) => ({
      pattern,
      message
    }),
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "请输入正确的邮箱地址"
    },
    phone: {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码"
    },
    username: {
      pattern: /^[a-zA-Z0-9_]{3,20}$/,
      message: "用户名只能包含字母、数字、下划线，长度3-20位"
    }
  };
  const _sfc_main$8 = /* @__PURE__ */ vue.defineComponent({
    __name: "login",
    setup(__props, { expose: __expose }) {
      __expose();
      const loginForm = vue.reactive({
        username: "",
        password: ""
      });
      const isLoading = vue.ref(false);
      const showPassword = vue.ref(false);
      const rememberPassword = vue.ref(false);
      const encryptPassword = (password) => {
        return btoa(password);
      };
      const decryptPassword = (encryptedPassword) => {
        try {
          return atob(encryptedPassword);
        } catch (error) {
          return "";
        }
      };
      const saveLoginInfo = () => {
        if (rememberPassword.value) {
          uni.setStorageSync("savedUsername", loginForm.username);
          uni.setStorageSync("savedPassword", encryptPassword(loginForm.password));
          uni.setStorageSync("rememberPassword", true);
        } else {
          uni.removeStorageSync("savedUsername");
          uni.removeStorageSync("savedPassword");
          uni.removeStorageSync("rememberPassword");
        }
      };
      const loadSavedLoginInfo = () => {
        const savedRemember = uni.getStorageSync("rememberPassword");
        if (savedRemember) {
          rememberPassword.value = true;
          loginForm.username = uni.getStorageSync("savedUsername") || "";
          const savedPassword = uni.getStorageSync("savedPassword");
          if (savedPassword) {
            loginForm.password = decryptPassword(savedPassword);
          }
        }
      };
      const clearUsername = () => {
        loginForm.username = "";
      };
      const clearPassword = () => {
        loginForm.password = "";
      };
      const togglePasswordVisibility = () => {
        showPassword.value = !showPassword.value;
      };
      const validationRules = {
        username: ValidationRules.required("请输入用户名"),
        password: ValidationRules.required("请输入密码")
      };
      const handleLogin = async () => {
        const validation = ErrorHandler.validateForm(loginForm, validationRules);
        if (!validation.isValid) {
          ErrorHandler.showValidationError(validation);
          return;
        }
        const result = await ErrorHandler.safeExecute(async () => {
          formatAppLog("log", "at pages/login/login.vue:94", "password", loginForm.password);
          const response = await API.auth.login({
            username: loginForm.username,
            password: loginForm.password
          });
          formatAppLog("log", "at pages/login/login.vue:99", "response", response);
          const loginData = ResponseHelper.getData(response);
          if (!loginData || !loginData.access_token || !loginData.refresh_token) {
            throw new Error("登录响应数据异常");
          }
          return loginData;
        }, {
          loadingRef: isLoading,
          showLoading: false,
          // 我们用自己的loading状态
          errorHandler: (error) => {
            formatAppLog("log", "at pages/login/login.vue:112", "error", error);
            ErrorHandler.handleApiError(error, {
              defaultMessage: "登录失败，请检查用户名和密码"
            });
          }
        });
        if (result.success) {
          setTokens(result.data.access_token, result.data.refresh_token);
          formatAppLog("log", "at pages/login/login.vue:122", "user", result.data.user);
          uni.setStorageSync("userInfo", result.data.user);
          saveLoginInfo();
          uni.showToast({
            title: "登录成功",
            icon: "success"
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/workspace/workspace"
            });
          }, 1500);
        }
      };
      vue.onMounted(() => {
        loadSavedLoginInfo();
      });
      const __returned__ = { loginForm, isLoading, showPassword, rememberPassword, encryptPassword, decryptPassword, saveLoginInfo, loadSavedLoginInfo, clearUsername, clearPassword, togglePasswordVisibility, validationRules, handleLogin };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  const _imports_0 = "/static/logo.png";
  function _sfc_render$7(_ctx, _cache, $props, $setup, $data, $options) {
    const _component_uni_icons = resolveEasycom(vue.resolveDynamicComponent("uni-icons"), __easycom_0);
    return vue.openBlock(), vue.createElementBlock("view", { class: "login-container" }, [
      vue.createCommentVNode(" 状态栏占位 "),
      vue.createElementVNode("view", { class: "status-bar" }),
      vue.createCommentVNode(" 头部logo区域 "),
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("image", {
          class: "logo",
          src: _imports_0,
          mode: "aspectFit"
        }),
        vue.createElementVNode("text", { class: "title" }, "织厂管理系统"),
        vue.createElementVNode("text", { class: "subtitle" }, "机器异常管理平台")
      ]),
      vue.createCommentVNode(" 登录表单 "),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-label" }, "用户名"),
          vue.createElementVNode("view", { class: "input-wrapper" }, [
            vue.createVNode(_component_uni_icons, {
              type: "person",
              size: "20",
              color: "#999999",
              class: "input-prefix-icon"
            }),
            vue.withDirectives(vue.createElementVNode(
              "input",
              {
                class: "form-input",
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.loginForm.username = $event),
                placeholder: "请输入用户名",
                "placeholder-class": "placeholder"
              },
              null,
              512
              /* NEED_PATCH */
            ), [
              [vue.vModelText, $setup.loginForm.username]
            ]),
            $setup.loginForm.username ? (vue.openBlock(), vue.createBlock(_component_uni_icons, {
              key: 0,
              type: "clear",
              size: "18",
              color: "#cccccc",
              class: "input-suffix-icon clear-icon",
              onClick: $setup.clearUsername
            })) : vue.createCommentVNode("v-if", true)
          ])
        ]),
        vue.createElementVNode("view", { class: "form-item" }, [
          vue.createElementVNode("view", { class: "form-label" }, "密码"),
          vue.createElementVNode("view", { class: "input-wrapper" }, [
            vue.createVNode(_component_uni_icons, {
              type: "locked",
              size: "20",
              color: "#999999",
              class: "input-prefix-icon"
            }),
            vue.withDirectives(vue.createElementVNode("input", {
              class: "form-input",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.loginForm.password = $event),
              placeholder: "请输入密码",
              "placeholder-class": "placeholder",
              password: !$setup.showPassword
            }, null, 8, ["password"]), [
              [vue.vModelText, $setup.loginForm.password]
            ]),
            $setup.loginForm.password ? (vue.openBlock(), vue.createBlock(_component_uni_icons, {
              key: 0,
              type: "clear",
              size: "18",
              color: "#cccccc",
              class: "input-suffix-icon clear-icon",
              onClick: $setup.clearPassword
            })) : vue.createCommentVNode("v-if", true),
            vue.createVNode(_component_uni_icons, {
              type: $setup.showPassword ? "eye-filled" : "eye-slash-filled",
              size: "20",
              color: $setup.showPassword ? "#2196F3" : "#999999",
              class: "input-suffix-icon eye-icon",
              onClick: $setup.togglePasswordVisibility
            }, null, 8, ["type", "color"])
          ])
        ]),
        vue.createCommentVNode(" 记住密码选项 "),
        vue.createElementVNode("view", { class: "remember-password" }, [
          vue.createElementVNode("view", {
            class: "checkbox-wrapper",
            onClick: _cache[2] || (_cache[2] = ($event) => $setup.rememberPassword = !$setup.rememberPassword)
          }, [
            vue.createElementVNode(
              "view",
              {
                class: vue.normalizeClass(["checkbox", { "checked": $setup.rememberPassword }])
              },
              [
                $setup.rememberPassword ? (vue.openBlock(), vue.createBlock(_component_uni_icons, {
                  key: 0,
                  type: "checkmarkempty",
                  size: "16",
                  color: "#ffffff"
                })) : vue.createCommentVNode("v-if", true)
              ],
              2
              /* CLASS */
            ),
            vue.createElementVNode("text", { class: "checkbox-label" }, "记住密码")
          ])
        ]),
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["login-btn", { "loading": $setup.isLoading }]),
          onClick: $setup.handleLogin,
          disabled: $setup.isLoading
        }, vue.toDisplayString($setup.isLoading ? "登录中..." : "登录"), 11, ["disabled"])
      ]),
      vue.createCommentVNode(" 底部信息 "),
      vue.createElementVNode("view", { class: "footer" }, [
        vue.createElementVNode("text", { class: "footer-text" }, "织厂机器异常管理系统 v1.0.0")
      ])
    ]);
  }
  const PagesLoginLogin = /* @__PURE__ */ _export_sfc(_sfc_main$8, [["render", _sfc_render$7], ["__file", "D:/learn/uniapptest/testvue3/src/pages/login/login.vue"]]);
  const USER_ROLES = {
    WEAVER: "weaver",
    // 织工
    MECHANIC: "mechanic"
    // 机修工
  };
  const USER_ROLE_NAMES = {
    [USER_ROLES.WEAVER]: "织工",
    [USER_ROLES.MECHANIC]: "机修工"
  };
  const USER_ROLE_BY_ID = {
    0: USER_ROLES.WEAVER,
    // 织工 workerType = 0
    1: USER_ROLES.MECHANIC
    // 机修工 workerType = 1
  };
  const MACHINE_STATUS = {
    NORMAL: "normal",
    // 正常
    ABNORMAL: "abnormal",
    // 异常
    REPAIRING: "repairing"
    // 维修中
  };
  const MACHINE_STATUS_NAMES = {
    [MACHINE_STATUS.NORMAL]: "正常",
    [MACHINE_STATUS.ABNORMAL]: "异常",
    [MACHINE_STATUS.REPAIRING]: "维修中"
  };
  const ANOMALY_SEVERITY = {
    LOW: "low",
    // 低
    MEDIUM: "medium",
    // 中
    HIGH: "high"
    // 高
  };
  const ANOMALY_SEVERITY_NAMES = {
    [ANOMALY_SEVERITY.LOW]: "低",
    [ANOMALY_SEVERITY.MEDIUM]: "中",
    [ANOMALY_SEVERITY.HIGH]: "高"
  };
  const ANOMALY_STATUS = {
    PENDING: "pending",
    // 待维修
    REPAIRING: "repairing",
    // 维修中
    COMPLETED: "completed"
    // 已完成
  };
  const ANOMALY_STATUS_NAMES = {
    [ANOMALY_STATUS.PENDING]: "待维修",
    [ANOMALY_STATUS.REPAIRING]: "维修中",
    [ANOMALY_STATUS.COMPLETED]: "已完成"
  };
  const REPAIR_STATUS = {
    IN_PROGRESS: "in_progress",
    // 进行中
    COMPLETED: "completed"
    // 已完成
  };
  const REPAIR_STATUS_NAMES = {
    [REPAIR_STATUS.IN_PROGRESS]: "进行中",
    [REPAIR_STATUS.COMPLETED]: "已完成"
  };
  const DisplayNameUtils = {
    // 获取用户角色显示名称
    getUserRoleName(role) {
      return USER_ROLE_NAMES[role] || role;
    },
    // 根据角色ID获取显示名称
    getUserRoleNameById(roleId) {
      const role = USER_ROLE_BY_ID[roleId];
      return role ? USER_ROLE_NAMES[role] : `未知角色(${roleId})`;
    },
    // 获取机器状态显示名称
    getMachineStatusName(status) {
      return MACHINE_STATUS_NAMES[status] || status;
    },
    // 获取异常严重程度显示名称
    getAnomalySeverityName(severity) {
      return ANOMALY_SEVERITY_NAMES[severity] || severity;
    },
    // 获取异常状态显示名称
    getAnomalyStatusName(status) {
      return ANOMALY_STATUS_NAMES[status] || status;
    },
    // 获取维修状态显示名称
    getRepairStatusName(status) {
      return REPAIR_STATUS_NAMES[status] || status;
    }
  };
  class User {
    constructor(data = {}) {
      this.id = data.id || data.worker_id || 0;
      this.username = data.username || data.worker_name || "";
      this.worker_name = data.worker_name || "";
      this.worker_type = data.worker_type !== void 0 ? data.worker_type : 0;
      this.role = data.role !== void 0 ? data.role : 0;
      this.work_class = data.work_class || "";
      this.dept = data.dept || "";
      this.sex = data.sex || "";
      this.name = data.name || data.worker_name || "";
      this.created_at = data.created_at || "";
      this.updated_at = data.updated_at || "";
    }
    // 获取角色显示名称
    getRoleDisplayName() {
      return DisplayNameUtils.getUserRoleNameById(this.role === "weaver" ? 0 : 1);
    }
    // 判断是否为织工 (workerType = 0)
    isWeaver() {
      return this.role === "weaver";
    }
    // 判断是否为机修工 (workerType = 1)
    isMechanic() {
      return this.role === "mechanic";
    }
    // 获取角色枚举值
    getRole() {
      return this.role === "weaver" ? "织工" : this.role === "mechanic" ? "机修工" : "unknown";
    }
  }
  class Machine {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.code = data.code || "";
      this.name = data.name || "";
      this.location = data.location || "";
      this.status = data.status || "normal";
      this.status_name = data.status_name || "";
      this.qr_code = data.qr_code || "";
      this.created_at = data.created_at || "";
      this.updated_at = data.updated_at || "";
    }
    // 获取状态显示名称（优先使用后端返回的status_name）
    getStatusDisplayName() {
      return this.status_name || DisplayNameUtils.getMachineStatusName(this.status);
    }
    // 判断机器状态
    isNormal() {
      return this.status === "normal";
    }
    isAbnormal() {
      return this.status === "abnormal";
    }
    isRepairing() {
      return this.status === "repairing";
    }
  }
  class Anomaly {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.machine_id = data.machine_id || 0;
      this.user_id = data.user_id || 0;
      this.description = data.description || "";
      this.severity = data.severity || "medium";
      this.severity_name = data.severity_name || "";
      this.status = data.status || "pending";
      this.status_name = data.status_name || "";
      this.remark = data.remark || "";
      this.created_at = data.created_at || "";
      this.updated_at = data.updated_at || "";
      this.machine = data.machine ? new Machine(data.machine) : null;
      this.user = data.user ? new User(data.user) : null;
    }
    // 获取严重程度显示名称
    getSeverityDisplayName() {
      return this.severity_name || DisplayNameUtils.getAnomalySeverityName(this.severity);
    }
    // 获取状态显示名称
    getStatusDisplayName() {
      return this.status_name || DisplayNameUtils.getAnomalyStatusName(this.status);
    }
    // 判断异常状态
    isPending() {
      return this.status === "pending";
    }
    isRepairing() {
      return this.status === "repairing";
    }
    isCompleted() {
      return this.status === "completed";
    }
    // 判断严重程度
    isLowSeverity() {
      return this.severity === "low";
    }
    isMediumSeverity() {
      return this.severity === "medium";
    }
    isHighSeverity() {
      return this.severity === "high";
    }
  }
  class Repair {
    constructor(data = {}) {
      this.id = data.id || 0;
      this.anomaly_id = data.anomaly_id || 0;
      this.mechanic_id = data.mechanic_id || 0;
      this.start_time = data.start_time || "";
      this.end_time = data.end_time || null;
      this.process = data.process || "";
      this.parts = data.parts || "";
      this.duration = data.duration || 0;
      this.status = data.status || "in_progress";
      this.status_name = data.status_name || "";
      this.created_at = data.created_at || "";
      this.updated_at = data.updated_at || "";
      this.anomaly = data.anomaly ? new Anomaly(data.anomaly) : null;
      this.mechanic = data.mechanic ? new User(data.mechanic) : null;
    }
    // 获取状态显示名称
    getStatusDisplayName() {
      return this.status_name || DisplayNameUtils.getRepairStatusName(this.status);
    }
    // 判断维修状态
    isInProgress() {
      return this.status === "in_progress";
    }
    isCompleted() {
      return this.status === "completed";
    }
    // 获取格式化的维修时长
    getFormattedDuration() {
      if (!this.duration)
        return "0分钟";
      const hours = Math.floor(this.duration / 60);
      const minutes = this.duration % 60;
      if (hours > 0) {
        return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
      }
      return `${minutes}分钟`;
    }
  }
  class ApiResponse {
    constructor(data = {}) {
      this.code = data.code || 0;
      this.message = data.message || "";
      this.data = data.data || null;
      this.total = data.total || 0;
      this.page = data.page || 1;
      this.size = data.size || 10;
    }
    // 判断是否成功
    isSuccess() {
      return this.code === 200;
    }
    // 获取数据
    getData() {
      return this.data;
    }
    // 获取分页信息
    getPagination() {
      return {
        total: this.total,
        page: this.page,
        size: this.size,
        hasMore: this.page * this.size < this.total
      };
    }
  }
  const DataUtils = {
    // 转换用户数据
    convertUser(data) {
      return new User(data);
    },
    // 转换用户列表
    convertUserList(dataList) {
      return Array.isArray(dataList) ? dataList.map((item) => new User(item)) : [];
    },
    // 转换机器数据
    convertMachine(data) {
      return new Machine(data);
    },
    // 转换机器列表
    convertMachineList(dataList) {
      return Array.isArray(dataList) ? dataList.map((item) => new Machine(item)) : [];
    },
    // 转换异常数据
    convertAnomaly(data) {
      return new Anomaly(data);
    },
    // 转换异常列表
    convertAnomalyList(dataList) {
      return Array.isArray(dataList) ? dataList.map((item) => new Anomaly(item)) : [];
    },
    // 转换维修数据
    convertRepair(data) {
      return new Repair(data);
    },
    // 转换维修列表
    convertRepairList(dataList) {
      return Array.isArray(dataList) ? dataList.map((item) => new Repair(item)) : [];
    },
    // 转换API响应
    convertApiResponse(data) {
      return new ApiResponse(data);
    }
  };
  var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
  function getDefaultExportFromCjs(x) {
    return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
  }
  var dayjs_min$1 = { exports: {} };
  (function(module, exports) {
    !function(t, e) {
      module.exports = e();
    }(commonjsGlobal, function() {
      var t = 1e3, e = 6e4, n = 36e5, r = "millisecond", i = "second", s = "minute", u = "hour", a = "day", o = "week", c = "month", f = "quarter", h = "year", d = "date", l = "Invalid Date", $ = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/, y = /\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g, M = { name: "en", weekdays: "Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"), months: "January_February_March_April_May_June_July_August_September_October_November_December".split("_"), ordinal: function(t2) {
        var e2 = ["th", "st", "nd", "rd"], n2 = t2 % 100;
        return "[" + t2 + (e2[(n2 - 20) % 10] || e2[n2] || e2[0]) + "]";
      } }, m = function(t2, e2, n2) {
        var r2 = String(t2);
        return !r2 || r2.length >= e2 ? t2 : "" + Array(e2 + 1 - r2.length).join(n2) + t2;
      }, v = { s: m, z: function(t2) {
        var e2 = -t2.utcOffset(), n2 = Math.abs(e2), r2 = Math.floor(n2 / 60), i2 = n2 % 60;
        return (e2 <= 0 ? "+" : "-") + m(r2, 2, "0") + ":" + m(i2, 2, "0");
      }, m: function t2(e2, n2) {
        if (e2.date() < n2.date())
          return -t2(n2, e2);
        var r2 = 12 * (n2.year() - e2.year()) + (n2.month() - e2.month()), i2 = e2.clone().add(r2, c), s2 = n2 - i2 < 0, u2 = e2.clone().add(r2 + (s2 ? -1 : 1), c);
        return +(-(r2 + (n2 - i2) / (s2 ? i2 - u2 : u2 - i2)) || 0);
      }, a: function(t2) {
        return t2 < 0 ? Math.ceil(t2) || 0 : Math.floor(t2);
      }, p: function(t2) {
        return { M: c, y: h, w: o, d: a, D: d, h: u, m: s, s: i, ms: r, Q: f }[t2] || String(t2 || "").toLowerCase().replace(/s$/, "");
      }, u: function(t2) {
        return void 0 === t2;
      } }, g = "en", D = {};
      D[g] = M;
      var p = "$isDayjsObject", S = function(t2) {
        return t2 instanceof _ || !(!t2 || !t2[p]);
      }, w = function t2(e2, n2, r2) {
        var i2;
        if (!e2)
          return g;
        if ("string" == typeof e2) {
          var s2 = e2.toLowerCase();
          D[s2] && (i2 = s2), n2 && (D[s2] = n2, i2 = s2);
          var u2 = e2.split("-");
          if (!i2 && u2.length > 1)
            return t2(u2[0]);
        } else {
          var a2 = e2.name;
          D[a2] = e2, i2 = a2;
        }
        return !r2 && i2 && (g = i2), i2 || !r2 && g;
      }, O = function(t2, e2) {
        if (S(t2))
          return t2.clone();
        var n2 = "object" == typeof e2 ? e2 : {};
        return n2.date = t2, n2.args = arguments, new _(n2);
      }, b = v;
      b.l = w, b.i = S, b.w = function(t2, e2) {
        return O(t2, { locale: e2.$L, utc: e2.$u, x: e2.$x, $offset: e2.$offset });
      };
      var _ = function() {
        function M2(t2) {
          this.$L = w(t2.locale, null, true), this.parse(t2), this.$x = this.$x || t2.x || {}, this[p] = true;
        }
        var m2 = M2.prototype;
        return m2.parse = function(t2) {
          this.$d = function(t3) {
            var e2 = t3.date, n2 = t3.utc;
            if (null === e2)
              return /* @__PURE__ */ new Date(NaN);
            if (b.u(e2))
              return /* @__PURE__ */ new Date();
            if (e2 instanceof Date)
              return new Date(e2);
            if ("string" == typeof e2 && !/Z$/i.test(e2)) {
              var r2 = e2.match($);
              if (r2) {
                var i2 = r2[2] - 1 || 0, s2 = (r2[7] || "0").substring(0, 3);
                return n2 ? new Date(Date.UTC(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2)) : new Date(r2[1], i2, r2[3] || 1, r2[4] || 0, r2[5] || 0, r2[6] || 0, s2);
              }
            }
            return new Date(e2);
          }(t2), this.init();
        }, m2.init = function() {
          var t2 = this.$d;
          this.$y = t2.getFullYear(), this.$M = t2.getMonth(), this.$D = t2.getDate(), this.$W = t2.getDay(), this.$H = t2.getHours(), this.$m = t2.getMinutes(), this.$s = t2.getSeconds(), this.$ms = t2.getMilliseconds();
        }, m2.$utils = function() {
          return b;
        }, m2.isValid = function() {
          return !(this.$d.toString() === l);
        }, m2.isSame = function(t2, e2) {
          var n2 = O(t2);
          return this.startOf(e2) <= n2 && n2 <= this.endOf(e2);
        }, m2.isAfter = function(t2, e2) {
          return O(t2) < this.startOf(e2);
        }, m2.isBefore = function(t2, e2) {
          return this.endOf(e2) < O(t2);
        }, m2.$g = function(t2, e2, n2) {
          return b.u(t2) ? this[e2] : this.set(n2, t2);
        }, m2.unix = function() {
          return Math.floor(this.valueOf() / 1e3);
        }, m2.valueOf = function() {
          return this.$d.getTime();
        }, m2.startOf = function(t2, e2) {
          var n2 = this, r2 = !!b.u(e2) || e2, f2 = b.p(t2), l2 = function(t3, e3) {
            var i2 = b.w(n2.$u ? Date.UTC(n2.$y, e3, t3) : new Date(n2.$y, e3, t3), n2);
            return r2 ? i2 : i2.endOf(a);
          }, $2 = function(t3, e3) {
            return b.w(n2.toDate()[t3].apply(n2.toDate("s"), (r2 ? [0, 0, 0, 0] : [23, 59, 59, 999]).slice(e3)), n2);
          }, y2 = this.$W, M3 = this.$M, m3 = this.$D, v2 = "set" + (this.$u ? "UTC" : "");
          switch (f2) {
            case h:
              return r2 ? l2(1, 0) : l2(31, 11);
            case c:
              return r2 ? l2(1, M3) : l2(0, M3 + 1);
            case o:
              var g2 = this.$locale().weekStart || 0, D2 = (y2 < g2 ? y2 + 7 : y2) - g2;
              return l2(r2 ? m3 - D2 : m3 + (6 - D2), M3);
            case a:
            case d:
              return $2(v2 + "Hours", 0);
            case u:
              return $2(v2 + "Minutes", 1);
            case s:
              return $2(v2 + "Seconds", 2);
            case i:
              return $2(v2 + "Milliseconds", 3);
            default:
              return this.clone();
          }
        }, m2.endOf = function(t2) {
          return this.startOf(t2, false);
        }, m2.$set = function(t2, e2) {
          var n2, o2 = b.p(t2), f2 = "set" + (this.$u ? "UTC" : ""), l2 = (n2 = {}, n2[a] = f2 + "Date", n2[d] = f2 + "Date", n2[c] = f2 + "Month", n2[h] = f2 + "FullYear", n2[u] = f2 + "Hours", n2[s] = f2 + "Minutes", n2[i] = f2 + "Seconds", n2[r] = f2 + "Milliseconds", n2)[o2], $2 = o2 === a ? this.$D + (e2 - this.$W) : e2;
          if (o2 === c || o2 === h) {
            var y2 = this.clone().set(d, 1);
            y2.$d[l2]($2), y2.init(), this.$d = y2.set(d, Math.min(this.$D, y2.daysInMonth())).$d;
          } else
            l2 && this.$d[l2]($2);
          return this.init(), this;
        }, m2.set = function(t2, e2) {
          return this.clone().$set(t2, e2);
        }, m2.get = function(t2) {
          return this[b.p(t2)]();
        }, m2.add = function(r2, f2) {
          var d2, l2 = this;
          r2 = Number(r2);
          var $2 = b.p(f2), y2 = function(t2) {
            var e2 = O(l2);
            return b.w(e2.date(e2.date() + Math.round(t2 * r2)), l2);
          };
          if ($2 === c)
            return this.set(c, this.$M + r2);
          if ($2 === h)
            return this.set(h, this.$y + r2);
          if ($2 === a)
            return y2(1);
          if ($2 === o)
            return y2(7);
          var M3 = (d2 = {}, d2[s] = e, d2[u] = n, d2[i] = t, d2)[$2] || 1, m3 = this.$d.getTime() + r2 * M3;
          return b.w(m3, this);
        }, m2.subtract = function(t2, e2) {
          return this.add(-1 * t2, e2);
        }, m2.format = function(t2) {
          var e2 = this, n2 = this.$locale();
          if (!this.isValid())
            return n2.invalidDate || l;
          var r2 = t2 || "YYYY-MM-DDTHH:mm:ssZ", i2 = b.z(this), s2 = this.$H, u2 = this.$m, a2 = this.$M, o2 = n2.weekdays, c2 = n2.months, f2 = n2.meridiem, h2 = function(t3, n3, i3, s3) {
            return t3 && (t3[n3] || t3(e2, r2)) || i3[n3].slice(0, s3);
          }, d2 = function(t3) {
            return b.s(s2 % 12 || 12, t3, "0");
          }, $2 = f2 || function(t3, e3, n3) {
            var r3 = t3 < 12 ? "AM" : "PM";
            return n3 ? r3.toLowerCase() : r3;
          };
          return r2.replace(y, function(t3, r3) {
            return r3 || function(t4) {
              switch (t4) {
                case "YY":
                  return String(e2.$y).slice(-2);
                case "YYYY":
                  return b.s(e2.$y, 4, "0");
                case "M":
                  return a2 + 1;
                case "MM":
                  return b.s(a2 + 1, 2, "0");
                case "MMM":
                  return h2(n2.monthsShort, a2, c2, 3);
                case "MMMM":
                  return h2(c2, a2);
                case "D":
                  return e2.$D;
                case "DD":
                  return b.s(e2.$D, 2, "0");
                case "d":
                  return String(e2.$W);
                case "dd":
                  return h2(n2.weekdaysMin, e2.$W, o2, 2);
                case "ddd":
                  return h2(n2.weekdaysShort, e2.$W, o2, 3);
                case "dddd":
                  return o2[e2.$W];
                case "H":
                  return String(s2);
                case "HH":
                  return b.s(s2, 2, "0");
                case "h":
                  return d2(1);
                case "hh":
                  return d2(2);
                case "a":
                  return $2(s2, u2, true);
                case "A":
                  return $2(s2, u2, false);
                case "m":
                  return String(u2);
                case "mm":
                  return b.s(u2, 2, "0");
                case "s":
                  return String(e2.$s);
                case "ss":
                  return b.s(e2.$s, 2, "0");
                case "SSS":
                  return b.s(e2.$ms, 3, "0");
                case "Z":
                  return i2;
              }
              return null;
            }(t3) || i2.replace(":", "");
          });
        }, m2.utcOffset = function() {
          return 15 * -Math.round(this.$d.getTimezoneOffset() / 15);
        }, m2.diff = function(r2, d2, l2) {
          var $2, y2 = this, M3 = b.p(d2), m3 = O(r2), v2 = (m3.utcOffset() - this.utcOffset()) * e, g2 = this - m3, D2 = function() {
            return b.m(y2, m3);
          };
          switch (M3) {
            case h:
              $2 = D2() / 12;
              break;
            case c:
              $2 = D2();
              break;
            case f:
              $2 = D2() / 3;
              break;
            case o:
              $2 = (g2 - v2) / 6048e5;
              break;
            case a:
              $2 = (g2 - v2) / 864e5;
              break;
            case u:
              $2 = g2 / n;
              break;
            case s:
              $2 = g2 / e;
              break;
            case i:
              $2 = g2 / t;
              break;
            default:
              $2 = g2;
          }
          return l2 ? $2 : b.a($2);
        }, m2.daysInMonth = function() {
          return this.endOf(c).$D;
        }, m2.$locale = function() {
          return D[this.$L];
        }, m2.locale = function(t2, e2) {
          if (!t2)
            return this.$L;
          var n2 = this.clone(), r2 = w(t2, e2, true);
          return r2 && (n2.$L = r2), n2;
        }, m2.clone = function() {
          return b.w(this.$d, this);
        }, m2.toDate = function() {
          return new Date(this.valueOf());
        }, m2.toJSON = function() {
          return this.isValid() ? this.toISOString() : null;
        }, m2.toISOString = function() {
          return this.$d.toISOString();
        }, m2.toString = function() {
          return this.$d.toUTCString();
        }, M2;
      }(), k = _.prototype;
      return O.prototype = k, [["$ms", r], ["$s", i], ["$m", s], ["$H", u], ["$W", a], ["$M", c], ["$y", h], ["$D", d]].forEach(function(t2) {
        k[t2[1]] = function(e2) {
          return this.$g(e2, t2[0], t2[1]);
        };
      }), O.extend = function(t2, e2) {
        return t2.$i || (t2(e2, _, O), t2.$i = true), O;
      }, O.locale = w, O.isDayjs = S, O.unix = function(t2) {
        return O(1e3 * t2);
      }, O.en = D[g], O.Ls = D, O.p = {}, O;
    });
  })(dayjs_min$1);
  var dayjs_minExports = dayjs_min$1.exports;
  const dayjs_min = /* @__PURE__ */ getDefaultExportFromCjs(dayjs_minExports);
  const dayjs = /* @__PURE__ */ _mergeNamespaces({
    __proto__: null,
    default: dayjs_min
  }, [dayjs_minExports]);
  class ScannerUtils {
    constructor() {
      this.scanReceiver = null;
      this.isPageActive = false;
      this.scanCallback = null;
      this.isInitialized = false;
      this.scanMode = "general";
      this.scanInterface = null;
      this.serviceConnection = null;
    }
    /**
     * 初始化扫码功能
     * @param {Function} callback 扫码成功回调函数
     * @param {Boolean} isPageActive 页面是否活跃
     * @param {String} mode 扫码模式: general/report/repair
     */
    async initScanner(callback, isPageActive = true, mode = "general") {
      formatAppLog("log", "at utils/scannerUtils.js:26", "initScaner", mode);
      this.scanCallback = callback;
      this.isPageActive = isPageActive;
      this.scanMode = mode;
      if (uni.getSystemInfoSync().platform !== "android") {
        formatAppLog("log", "at utils/scannerUtils.js:33", "非安卓平台，使用uni.scanCode");
        return this.useUniScanCode();
      }
      try {
        const sunmiResult = await this.initSunmiScanner();
        formatAppLog("log", "at utils/scannerUtils.js:40", "sunmiResult", sunmiResult);
        if (sunmiResult.success) {
          return sunmiResult;
        }
        formatAppLog("log", "at utils/scannerUtils.js:46", "商米扫码头初始化失败，尝试新大陆扫码头");
        return await this.initNewlandScanner();
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:50", "初始化扫码功能失败：", error);
        return this.useUniScanCode();
      }
    }
    /**
     * 初始化商米扫码头
     */
    async initSunmiScanner() {
      try {
        const main = plus.android.runtimeMainActivity();
        await this.registerSunmiBroadcastReceiver(main);
        formatAppLog("log", "at utils/scannerUtils.js:65", "商米广播接收器注册成功");
        try {
          await this.bindSunmiScannerService(main);
          formatAppLog("log", "at utils/scannerUtils.js:70", "商米扫码服务绑定成功");
        } catch (serviceError) {
          formatAppLog("log", "at utils/scannerUtils.js:72", "商米扫码服务绑定失败，但广播接收器已注册，扫码功能仍可用:", serviceError);
        }
        this.isInitialized = true;
        formatAppLog("log", "at utils/scannerUtils.js:77", "商米扫码头初始化完成");
        return {
          success: true,
          message: "商米扫码功能已启用，请使用扫码枪扫描"
        };
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:84", "商米扫码头初始化失败：", error);
        throw error;
      }
    }
    /**
     * 绑定商米扫码服务
     */
    async bindSunmiScannerService(main) {
      try {
        const Intent = plus.android.importClass("android.content.Intent");
        const Context = plus.android.importClass("android.content.Context");
        this.serviceConnection = plus.android.implements("android.content.ServiceConnection", {
          onServiceConnected: (name, service) => {
            formatAppLog("log", "at utils/scannerUtils.js:100", "商米扫码服务连接成功");
            try {
              const IScanInterface = plus.android.importClass("com.sunmi.scanner.IScanInterface");
              if (IScanInterface && IScanInterface.Stub) {
                this.scanInterface = IScanInterface.Stub.asInterface(service);
                formatAppLog("log", "at utils/scannerUtils.js:106", "IScanInterface获取成功");
              }
            } catch (interfaceError) {
              formatAppLog("log", "at utils/scannerUtils.js:109", "获取IScanInterface失败，但服务已连接:", interfaceError);
            }
          },
          onServiceDisconnected: (name) => {
            formatAppLog("log", "at utils/scannerUtils.js:113", "商米扫码服务断开连接");
            this.scanInterface = null;
          }
        });
        const intent = new Intent();
        intent.setPackage("com.sunmi.scanner");
        intent.setAction("com.sunmi.scanner.IScanInterface");
        try {
          main.startService(intent);
          formatAppLog("log", "at utils/scannerUtils.js:125", "商米扫码服务启动成功");
        } catch (startError) {
          formatAppLog("log", "at utils/scannerUtils.js:127", "启动服务失败，尝试直接绑定:", startError);
        }
        const BIND_AUTO_CREATE = Context.BIND_AUTO_CREATE || 1;
        const bindResult = main.bindService(intent, this.serviceConnection, BIND_AUTO_CREATE);
        if (!bindResult) {
          formatAppLog("log", "at utils/scannerUtils.js:135", "bindService返回false，可能服务不存在");
          throw new Error("绑定商米扫码服务失败：服务不可用");
        }
        formatAppLog("log", "at utils/scannerUtils.js:139", "商米扫码服务绑定请求已发送");
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("服务连接超时"));
          }, 5e3);
          const originalOnServiceConnected = this.serviceConnection.onServiceConnected;
          this.serviceConnection.onServiceConnected = (name, service) => {
            clearTimeout(timeout);
            originalOnServiceConnected(name, service);
            resolve();
          };
        });
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:156", "绑定商米扫码服务失败：", error);
        throw error;
      }
    }
    /**
     * 注册商米广播接收器
     */
    async registerSunmiBroadcastReceiver(main) {
      try {
        const IntentFilter = plus.android.importClass("android.content.IntentFilter");
        const BroadcastReceiver = plus.android.importClass("android.content.BroadcastReceiver");
        const filter = new IntentFilter();
        filter.addAction("com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED");
        this.scanReceiver = plus.android.implements(
          "io.dcloud.feature.internal.reflect.BroadcastReceiver",
          {
            onReceive: (context, intent) => {
              formatAppLog("log", "at utils/scannerUtils.js:176", "商米扫码广播接收，isPageActive:", this.isPageActive);
              if (!this.isPageActive)
                return;
              try {
                const scanResult = intent.getStringExtra("data");
                const sourceBytes = intent.getByteArrayExtra("source_byte");
                formatAppLog("log", "at utils/scannerUtils.js:184", "商米扫码结果:", scanResult, sourceBytes);
                if (scanResult && this.scanCallback) {
                  this.handleScanResult(scanResult);
                }
              } catch (error) {
                formatAppLog("error", "at utils/scannerUtils.js:191", "处理商米广播数据时出错：", error);
                if (this.scanCallback) {
                  this.scanCallback({
                    success: false,
                    error: error.message
                  });
                }
              }
            }
          }
        );
        main.registerReceiver(this.scanReceiver, filter);
        formatAppLog("log", "at utils/scannerUtils.js:205", "商米扫码广播接收器注册成功");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:207", "注册商米广播接收器失败：", error);
        throw error;
      }
    }
    /**
     * 初始化新大陆扫码头（作为备选方案）
     */
    async initNewlandScanner() {
      try {
        const main = plus.android.runtimeMainActivity();
        await this.configureNewlandScannerBroadcast(main);
        await this.registerNewlandBroadcastReceiver(main);
        this.isInitialized = true;
        formatAppLog("log", "at utils/scannerUtils.js:226", "新大陆扫码头初始化成功");
        return {
          success: true,
          message: "新大陆扫码功能已启用，请使用扫码枪扫描"
        };
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:233", "新大陆扫码头初始化失败：", error);
        throw error;
      }
    }
    /**
     * 配置新大陆扫码枪广播设置
     */
    async configureNewlandScannerBroadcast(main) {
      try {
        const Intent = plus.android.importClass("android.content.Intent");
        const intent = new Intent("com.android.scanner.service_settings");
        intent.putExtra(
          "action_barcode_broadcast",
          "com.android.server.scannerservice.broadcast"
        );
        intent.putExtra("key_barcode_broadcast", "scannerdata");
        main.sendBroadcast(intent);
        formatAppLog("log", "at utils/scannerUtils.js:253", "新大陆扫码枪广播配置完成");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:255", "配置新大陆扫码枪广播失败：", error);
        throw error;
      }
    }
    /**
     * 注册新大陆广播接收器
     */
    async registerNewlandBroadcastReceiver(main) {
      try {
        const IntentFilter = plus.android.importClass("android.content.IntentFilter");
        const filter = new IntentFilter();
        filter.addAction("com.android.server.scannerservice.broadcast");
        this.scanReceiver = plus.android.implements(
          "io.dcloud.feature.internal.reflect.BroadcastReceiver",
          {
            onReceive: (context, intent) => {
              formatAppLog("log", "at utils/scannerUtils.js:273", "新大陆扫码广播接收，isPageActive:", this.isPageActive);
              if (!this.isPageActive)
                return;
              try {
                const scanResult = intent.getStringExtra("scannerdata");
                formatAppLog("log", "at utils/scannerUtils.js:279", "新大陆扫码结果:", scanResult);
                if (scanResult && this.scanCallback) {
                  this.handleScanResult(scanResult);
                }
              } catch (error) {
                formatAppLog("error", "at utils/scannerUtils.js:286", "处理新大陆广播数据时出错：", error);
                if (this.scanCallback) {
                  this.scanCallback({
                    success: false,
                    error: error.message
                  });
                }
              }
            }
          }
        );
        main.registerReceiver(this.scanReceiver, filter);
        formatAppLog("log", "at utils/scannerUtils.js:300", "新大陆扫码广播接收器注册成功");
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:302", "注册新大陆广播接收器失败：", error);
        throw error;
      }
    }
    /**
     * 使用uni.scanCode作为降级方案
     */
    useUniScanCode() {
      uni.scanCode({
        success: (res) => {
          if (this.scanCallback) {
            this.handleScanResult(res.result);
          }
        },
        fail: (err) => {
          formatAppLog("error", "at utils/scannerUtils.js:319", "扫码失败:", err);
          if (this.scanCallback) {
            this.scanCallback({
              success: false,
              error: err.errMsg || "扫码失败"
            });
          }
        }
      });
      return {
        success: true,
        message: "使用系统扫码功能"
      };
    }
    /**
     * 设置页面活跃状态
     */
    setPageActive(isActive) {
      this.isPageActive = isActive;
    }
    /**
     * 销毁扫码功能
     */
    destroy() {
      if (this.scanReceiver && uni.getSystemInfoSync().platform === "android") {
        try {
          const main = plus.android.runtimeMainActivity();
          main.unregisterReceiver(this.scanReceiver);
          formatAppLog("log", "at utils/scannerUtils.js:350", "扫码广播接收器已注销");
        } catch (error) {
          formatAppLog("error", "at utils/scannerUtils.js:352", "注销广播接收器失败：", error);
        }
      }
      if (this.serviceConnection && uni.getSystemInfoSync().platform === "android") {
        try {
          const main = plus.android.runtimeMainActivity();
          main.unbindService(this.serviceConnection);
          formatAppLog("log", "at utils/scannerUtils.js:361", "商米扫码服务已解绑");
        } catch (error) {
          formatAppLog("error", "at utils/scannerUtils.js:363", "解绑商米扫码服务失败：", error);
        }
      }
      this.scanReceiver = null;
      this.serviceConnection = null;
      this.scanInterface = null;
      this.scanCallback = null;
      this.isInitialized = false;
    }
    /**
     * 手动触发扫码（用于按钮点击等场景）
     */
    startScan() {
      if (!this.isInitialized) {
        return this.useUniScanCode();
      }
      uni.showToast({
        title: "请使用扫码枪扫描",
        icon: "none",
        duration: 2e3
      });
    }
    /**
     * 根据扫码模式处理扫码结果
     * @param {String} scanResult 扫码结果
     */
    async handleScanResult(scanResult) {
      formatAppLog("log", "at utils/scannerUtils.js:396", "处理扫码结果:", scanResult, "模式:", this.scanMode);
      try {
        let response = null;
        switch (this.scanMode) {
          case "report":
            response = await API.qr.scanForReport(scanResult);
            break;
          case "repair":
            response = await API.qr.scanForRepair(scanResult);
            break;
          default:
            response = await API.qr.scan(scanResult);
            break;
        }
        const apiData = ResponseHelper.getData(response);
        if (this.scanCallback) {
          this.scanCallback({
            success: true,
            result: scanResult,
            scanType: "QR_CODE",
            mode: this.scanMode,
            data: apiData
          });
        }
      } catch (error) {
        formatAppLog("error", "at utils/scannerUtils.js:431", "API调用失败:", error);
        if (this.scanCallback) {
          this.scanCallback({
            success: true,
            result: scanResult,
            scanType: "QR_CODE",
            mode: this.scanMode,
            apiError: error.message,
            data: null
          });
        }
      }
    }
    /**
     * 设置扫码模式
     * @param {String} mode 扫码模式
     */
    setScanMode(mode) {
      this.scanMode = mode;
      formatAppLog("log", "at utils/scannerUtils.js:453", "扫码模式已设置为:", mode);
    }
  }
  const scannerUtils = new ScannerUtils();
  const __default__$3 = {
    onShow() {
      formatAppLog("log", "at pages/workspace/workspace.vue:545", "工作台页面显示，激活扫码功能");
      scannerUtils.setPageActive(true);
    },
    onHide() {
      formatAppLog("log", "at pages/workspace/workspace.vue:549", "工作台页面隐藏，停用扫码功能");
      scannerUtils.setPageActive(false);
    },
    onUnload() {
      formatAppLog("log", "at pages/workspace/workspace.vue:553", "工作台页面卸载，清理扫码资源");
      scannerUtils.destroy();
    }
  };
  const _sfc_main$7 = /* @__PURE__ */ vue.defineComponent({
    ...__default__$3,
    __name: "workspace",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref(null);
      const stats = vue.ref({
        todayTasks: 0,
        completedTasks: 0,
        pendingTasks: 0,
        totalTasks: 0
      });
      const quickActions = vue.ref([]);
      const reportForm = vue.reactive({
        qrCode: "",
        machineCode: "",
        machineName: "",
        reason: "",
        remark: ""
      });
      const reportReasons = [
        { value: "断针", label: "断针" },
        { value: "皮带松动", label: "皮带松动" },
        { value: "维护保养", label: "维护保养" },
        { value: "其他", label: "其他" }
      ];
      const reportHistory = vue.ref([]);
      const getReportHistory = async () => {
        try {
          formatAppLog("log", "at pages/workspace/workspace.vue:53", "开始获取异常上报历史记录");
          const response = await API.anomalies.getList({
            page: 1,
            page_size: 5
            // 只显示最近5条记录
          });
          formatAppLog("log", "at pages/workspace/workspace.vue:61", "异常记录API响应:", response);
          if (ResponseHelper.isSuccess(response)) {
            const data = ResponseHelper.getData(response);
            reportHistory.value = data.map((item) => {
              var _a, _b;
              return {
                id: item.id,
                machineCode: ((_a = item.machine) == null ? void 0 : _a.code) || "Unknown",
                machineName: ((_b = item.machine) == null ? void 0 : _b.name) || "未知设备",
                reportTime: dayjs(item.created_at).format("YYYY-MM-DD HH:mm"),
                status: item.status,
                description: item.description,
                severity: item.severity,
                remark: item.remark
              };
            });
            formatAppLog("log", "at pages/workspace/workspace.vue:78", "转换后的历史记录:", reportHistory.value);
          } else {
            formatAppLog("error", "at pages/workspace/workspace.vue:80", "获取历史记录失败:", ResponseHelper.getErrorMessage(response));
          }
        } catch (error) {
          formatAppLog("error", "at pages/workspace/workspace.vue:84", "获取历史记录出错:", error);
        }
      };
      const getStatusInfo = (status) => {
        const statusMap = {
          pending: { text: ANOMALY_STATUS_NAMES.pending, color: "#FF9800" },
          repairing: { text: ANOMALY_STATUS_NAMES.repairing, color: "#2196F3" },
          completed: { text: ANOMALY_STATUS_NAMES.completed, color: "#4CAF50" }
        };
        return statusMap[status] || { text: "未知", color: "#999999" };
      };
      const getUserInfo = async () => {
        try {
          formatAppLog("log", "at pages/workspace/workspace.vue:102", "开始获取用户信息");
          const token = uni.getStorageSync("access_token");
          if (!token) {
            formatAppLog("log", "at pages/workspace/workspace.vue:107", "未找到访问令牌，跳转到登录页");
            uni.reLaunch({
              url: "/pages/login/login"
            });
            return;
          }
          const response = await API.auth.getUserInfo();
          formatAppLog("log", "at pages/workspace/workspace.vue:116", "用户信息API响应:", response);
          if (ResponseHelper.isSuccess(response)) {
            const apiUserInfo = ResponseHelper.getData(response);
            userInfo.value = DataUtils.convertUser(apiUserInfo);
            uni.setStorageSync("userInfo", apiUserInfo);
            formatAppLog("log", "at pages/workspace/workspace.vue:125", "用户信息获取成功:", userInfo.value);
            if (userInfo.value.isWeaver()) {
              quickActions.value = [
                { icon: "📱", text: "扫码上报", action: "scan" },
                { icon: "📋", text: "上报记录", action: "history" },
                { icon: "📊", text: "工作统计", action: "stats" },
                { icon: "🔧", text: "设备状态", action: "equipment" }
              ];
              await getReportHistory();
            } else if (userInfo.value.isMechanic()) {
              quickActions.value = [
                { icon: "🔧", text: "维修任务", path: "/pages/mechanic/home" },
                { icon: "📝", text: "维修记录", action: "repair_history" },
                { icon: "📊", text: "工作统计", action: "stats" },
                { icon: "⚙️", text: "设备管理", action: "equipment" }
              ];
            }
          } else {
            throw new Error(ResponseHelper.getErrorMessage(response));
          }
        } catch (error) {
          formatAppLog("error", "at pages/workspace/workspace.vue:150", "获取用户信息失败:", error);
          const localUserInfo = uni.getStorageSync("userInfo");
          if (localUserInfo) {
            formatAppLog("log", "at pages/workspace/workspace.vue:155", "使用本地存储的用户信息作为兜底");
            userInfo.value = DataUtils.convertUser(localUserInfo);
            if (userInfo.value.isWeaver()) {
              quickActions.value = [
                { icon: "📱", text: "扫码上报", action: "scan" },
                { icon: "📋", text: "上报记录", action: "history" }
              ];
            } else if (userInfo.value.isMechanic()) {
              quickActions.value = [
                { icon: "🔧", text: "维修任务", path: "/pages/mechanic/home" },
                { icon: "📝", text: "维修记录", action: "repair_history" }
              ];
            }
          } else {
            formatAppLog("log", "at pages/workspace/workspace.vue:172", "本地也没有用户信息，跳转到登录页");
            uni.showToast({
              title: "请重新登录",
              icon: "none"
            });
            setTimeout(() => {
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }, 1500);
          }
        }
      };
      const getStats = async () => {
        var _a, _b;
        try {
          if ((_a = userInfo.value) == null ? void 0 : _a.isWeaver()) {
            const response = await API.anomalies.getList({ page: 1, page_size: 100 });
            if (ResponseHelper.isSuccess(response)) {
              const data = ResponseHelper.getData(response);
              const today = (/* @__PURE__ */ new Date()).toDateString();
              const todayTasks = data.filter(
                (item) => new Date(item.created_at).toDateString() === today
              ).length;
              const completedTasks = data.filter(
                (item) => item.status === "completed"
              ).length;
              const pendingTasks = data.filter(
                (item) => item.status === "pending"
              ).length;
              stats.value = {
                todayTasks,
                completedTasks,
                pendingTasks,
                totalTasks: data.length
              };
            } else {
              stats.value = { todayTasks: 0, completedTasks: 0, pendingTasks: 0, totalTasks: 0 };
            }
          } else if ((_b = userInfo.value) == null ? void 0 : _b.isMechanic()) {
            stats.value = {
              todayTasks: 5,
              completedTasks: 3,
              pendingTasks: 2,
              totalTasks: 28
            };
          }
        } catch (error) {
          formatAppLog("error", "at pages/workspace/workspace.vue:232", "获取统计数据失败:", error);
          stats.value = { todayTasks: 0, completedTasks: 0, pendingTasks: 0, totalTasks: 0 };
        }
      };
      const showReportModal = (qrCode, machine) => {
        formatAppLog("log", "at pages/workspace/workspace.vue:240", "showReportModal called", qrCode, machine);
        reportForm.qrCode = qrCode;
        reportForm.machineCode = (machine == null ? void 0 : machine.code) || qrCode;
        reportForm.machineName = (machine == null ? void 0 : machine.name) || `机器-${qrCode}`;
        reportForm.reason = "";
        reportForm.remark = "";
        formatAppLog("log", "at pages/workspace/workspace.vue:247", "reportForm filled:", reportForm);
        uni.showActionSheet({
          itemList: reportReasons.map((item) => item.label),
          success: (res) => {
            const selectedReason = reportReasons[res.tapIndex];
            reportForm.reason = selectedReason.value;
            if (selectedReason.value === "其他") {
              uni.showModal({
                title: "异常上报",
                content: `机台：${reportForm.machineName} (${reportForm.machineCode})
上报原因：${reportForm.reason}

请输入详细说明：`,
                editable: true,
                placeholderText: "请输入备注信息",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    reportForm.remark = modalRes.content || "";
                    submitReport();
                  }
                }
              });
            } else {
              uni.showModal({
                title: "确认上报",
                content: `机台：${reportForm.machineName} (${reportForm.machineCode})
上报原因：${reportForm.reason}

确认提交异常上报？`,
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    submitReport();
                  }
                }
              });
            }
          }
        });
      };
      const closeReportModal = () => {
      };
      const submitReport = async () => {
        var _a;
        formatAppLog("log", "at pages/workspace/workspace.vue:295", "开始提交工作台异常上报");
        formatAppLog("log", "at pages/workspace/workspace.vue:296", "reportForm:", reportForm);
        if (!reportForm.reason) {
          uni.showToast({
            title: "请选择上报原因",
            icon: "none"
          });
          return;
        }
        if (reportForm.reason === "其他" && !reportForm.remark) {
          uni.showToast({
            title: "请填写备注信息",
            icon: "none"
          });
          return;
        }
        try {
          let machineData = null;
          try {
            const qrResponse = await API.machines.getByQRCode(reportForm.qrCode);
            if (ResponseHelper.isSuccess(qrResponse)) {
              machineData = ResponseHelper.getData(qrResponse).machine;
            }
          } catch (error) {
            formatAppLog("log", "at pages/workspace/workspace.vue:323", "通过QR码获取机器信息失败，尝试使用机器码:", error);
            try {
              const codeResponse = await API.machines.getByCode(reportForm.machineCode);
              if (ResponseHelper.isSuccess(codeResponse)) {
                machineData = ResponseHelper.getData(codeResponse).machine;
              }
            } catch (codeError) {
              formatAppLog("log", "at pages/workspace/workspace.vue:330", "通过机器码获取机器信息也失败:", codeError);
            }
          }
          const machineId = (machineData == null ? void 0 : machineData.id) || 1;
          const reportData = {
            machine_id: machineId,
            description: reportForm.reason === "其他" ? reportForm.remark : reportForm.reason,
            severity: "medium",
            // 工作台快速上报默认为中等严重程度
            remark: reportForm.reason === "其他" ? "" : reportForm.remark || ""
          };
          formatAppLog("log", "at pages/workspace/workspace.vue:345", "准备发送的上报数据:", reportData);
          if (!reportData.machine_id || !reportData.description || !reportData.severity) {
            throw new Error("数据不完整：" + JSON.stringify({
              machine_id: reportData.machine_id,
              description: reportData.description,
              severity: reportData.severity
            }));
          }
          const response = await API.anomalies.create(reportData);
          formatAppLog("log", "at pages/workspace/workspace.vue:359", "API响应:", response);
          if (ResponseHelper.isSuccess(response)) {
            uni.showToast({
              title: "上报成功",
              icon: "success"
            });
            closeReportModal();
            if ((_a = userInfo.value) == null ? void 0 : _a.isWeaver()) {
              await Promise.all([
                getReportHistory(),
                getStats()
              ]);
            }
          } else {
            throw new Error(ResponseHelper.getErrorMessage(response));
          }
        } catch (error) {
          formatAppLog("error", "at pages/workspace/workspace.vue:379", "上报失败详细信息:", error);
          uni.showToast({
            title: error.message || "上报失败，请重试",
            icon: "none"
          });
        }
      };
      const handleScanReport = async () => {
        var _a;
        try {
          const scanMode = ((_a = userInfo.value) == null ? void 0 : _a.isWeaver()) ? "report" : "repair";
          const initResult = await scannerUtils.initScanner((result) => {
            var _a2, _b;
            if (result.success) {
              formatAppLog("log", "at pages/workspace/workspace.vue:396", "扫码结果:", result);
              if (result.apiError) {
                uni.showToast({
                  title: result.apiError,
                  icon: "none",
                  duration: 2e3
                });
                if ((_a2 = userInfo.value) == null ? void 0 : _a2.isWeaver()) {
                  uni.navigateTo({
                    url: `/pages/report/report?qrCode=${result.result}`
                  });
                } else {
                  uni.navigateTo({
                    url: `/pages/repair/repair?qrCode=${result.result}`
                  });
                }
              } else if (result.data) {
                if ((_b = userInfo.value) == null ? void 0 : _b.isWeaver()) {
                  formatAppLog("log", "at pages/workspace/workspace.vue:420", "result", result, userInfo.value);
                  showReportModal(result.result, result.data.machine);
                } else {
                  const { machine, pending_anomalies, repairing_anomalies } = result.data;
                  if (pending_anomalies.length > 0 || repairing_anomalies.length > 0) {
                    uni.navigateTo({
                      url: `/pages/repair/repair?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data))}`
                    });
                  } else {
                    uni.showModal({
                      title: "提示",
                      content: `机器 ${machine.name} 当前状态正常，无需维修`,
                      showCancel: false
                    });
                  }
                }
              }
            } else {
              formatAppLog("error", "at pages/workspace/workspace.vue:441", "扫码失败:", result.error);
              uni.showToast({
                title: result.error || "扫码失败",
                icon: "none"
              });
            }
          }, true, scanMode);
          if (initResult.success) {
            uni.showToast({
              title: "请扫描机器二维码",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/workspace/workspace.vue:457", "初始化扫码失败:", error);
          uni.showToast({
            title: "扫码功能初始化失败",
            icon: "none"
          });
        }
      };
      const viewReportDetail = (report) => {
        const statusInfo = getStatusInfo(report.status);
        let content = `机器：${report.machineName} (${report.machineCode})
`;
        content += `上报时间：${report.reportTime}
`;
        content += `状态：${statusInfo.text}
`;
        content += `严重程度：${getSeverityText(report.severity)}
`;
        content += `描述：${report.description}`;
        if (report.remark) {
          content += `
备注：${report.remark}`;
        }
        uni.showModal({
          title: "上报详情",
          content,
          showCancel: false
        });
      };
      const getSeverityText = (severity) => {
        const severityMap = {
          low: "轻微",
          medium: "中等",
          high: "严重",
          critical: "紧急"
        };
        return severityMap[severity] || "未知";
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              API.auth.logout();
            }
          }
        });
      };
      const handleQuickAction = (action) => {
        if (action.path) {
          uni.navigateTo({
            url: action.path
          });
        } else if (action.action === "scan") {
          handleScanReport();
        } else {
          uni.showToast({
            title: "功能开发中",
            icon: "none"
          });
        }
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        if (uni.getSystemInfoSync().platform === "android") {
          handleScanReport();
        }
        await getUserInfo();
        await getStats();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { userInfo, stats, quickActions, reportForm, reportReasons, reportHistory, getReportHistory, getStatusInfo, getUserInfo, getStats, showReportModal, closeReportModal, submitReport, handleScanReport, viewReportDetail, getSeverityText, handleLogout, handleQuickAction };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$6(_ctx, _cache, $props, $setup, $data, $options) {
    var _a, _b, _c, _d;
    return vue.openBlock(), vue.createElementBlock("view", { class: "workspace-container" }, [
      vue.createCommentVNode(" 欢迎信息 "),
      vue.createElementVNode("view", { class: "welcome-section" }, [
        vue.createElementVNode("view", { class: "welcome-content" }, [
          vue.createElementVNode("view", { class: "welcome-info" }, [
            vue.createElementVNode(
              "text",
              { class: "welcome-text" },
              "你好，" + vue.toDisplayString(((_a = $setup.userInfo) == null ? void 0 : _a.name) || ((_b = $setup.userInfo) == null ? void 0 : _b.username)),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "role-text" },
              vue.toDisplayString((_c = $setup.userInfo) == null ? void 0 : _c.getRoleDisplayName()) + "工作台",
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", {
            class: "logout-btn",
            onClick: $setup.handleLogout
          }, [
            vue.createElementVNode("text", { class: "logout-text" }, "退出")
          ])
        ])
      ]),
      vue.createCommentVNode(" 统计卡片 "),
      vue.createElementVNode("view", { class: "stats-section" }, [
        vue.createElementVNode("view", { class: "stats-card" }, [
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.todayTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "今日任务")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.completedTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "已完成")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.pendingTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "待处理")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.totalTasks),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "总任务")
          ])
        ])
      ]),
      vue.createCommentVNode(" 快捷功能 "),
      vue.createElementVNode("view", { class: "quick-actions" }, [
        vue.createElementVNode("view", { class: "section-title" }, "快捷功能"),
        vue.createElementVNode("view", { class: "actions-grid" }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.quickActions, (action, index) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "action-item",
                key: index,
                onClick: ($event) => $setup.handleQuickAction(action)
              }, [
                vue.createElementVNode(
                  "view",
                  { class: "action-icon" },
                  vue.toDisplayString(action.icon),
                  1
                  /* TEXT */
                ),
                vue.createElementVNode(
                  "text",
                  { class: "action-text" },
                  vue.toDisplayString(action.text),
                  1
                  /* TEXT */
                )
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])
      ]),
      vue.createCommentVNode(" 织工历史记录 "),
      ((_d = $setup.userInfo) == null ? void 0 : _d.isWeaver()) ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "recent-section"
      }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("view", { class: "section-title" }, "最近上报"),
          vue.createCommentVNode(' <text class="view-all">查看全部</text> ')
        ]),
        $setup.reportHistory.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "history-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.reportHistory, (item) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "history-item",
                key: item.id,
                onClick: ($event) => $setup.viewReportDetail(item)
              }, [
                vue.createElementVNode("view", { class: "machine-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "machine-name" },
                    vue.toDisplayString(item.machineName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "machine-code" },
                    vue.toDisplayString(item.machineCode),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "report-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "report-time" },
                    vue.toDisplayString(item.reportTime),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-tag",
                      style: vue.normalizeStyle({ backgroundColor: $setup.getStatusInfo(item.status).color })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(item.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : (vue.openBlock(), vue.createElementBlock(
          vue.Fragment,
          { key: 1 },
          [
            vue.createCommentVNode(" 暂无记录提示 "),
            vue.createElementVNode("view", { class: "empty-state" }, [
              vue.createElementVNode("text", { class: "empty-text" }, "暂无异常上报记录")
            ])
          ],
          2112
          /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
        ))
      ])) : (vue.openBlock(), vue.createElementBlock(
        vue.Fragment,
        { key: 1 },
        [
          vue.createCommentVNode(" 机修工最近活动 "),
          vue.createElementVNode("view", { class: "recent-section" }, [
            vue.createElementVNode("view", { class: "section-title" }, "最近活动"),
            vue.createElementVNode("view", { class: "activity-list" }, [
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "🔧"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "设备维修完成"),
                  vue.createElementVNode("text", { class: "activity-time" }, "2小时前")
                ])
              ]),
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "📱"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "异常上报"),
                  vue.createElementVNode("text", { class: "activity-time" }, "4小时前")
                ])
              ]),
              vue.createElementVNode("view", { class: "activity-item" }, [
                vue.createElementVNode("view", { class: "activity-icon" }, "✅"),
                vue.createElementVNode("view", { class: "activity-content" }, [
                  vue.createElementVNode("text", { class: "activity-title" }, "任务完成"),
                  vue.createElementVNode("text", { class: "activity-time" }, "昨天")
                ])
              ])
            ])
          ])
        ],
        2112
        /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
      ))
    ]);
  }
  const PagesWorkspaceWorkspace = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["render", _sfc_render$6], ["__file", "D:/learn/uniapptest/testvue3/src/pages/workspace/workspace.vue"]]);
  const _sfc_main$6 = /* @__PURE__ */ vue.defineComponent({
    __name: "profile",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({
        username: "",
        role: "",
        roleText: ""
      });
      const getUserInfo = () => {
        const info = uni.getStorageSync("userInfo");
        if (info) {
          userInfo.value = {
            username: info.username,
            role: info.role,
            roleText: info.role === "worker" ? "织工" : "机修工"
          };
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "提示",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(() => {
        getUserInfo();
      });
      const __returned__ = { userInfo, getUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$5(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "profile-container" }, [
      vue.createCommentVNode(" 用户信息卡片 "),
      vue.createElementVNode("view", { class: "user-card" }, [
        vue.createElementVNode("view", { class: "avatar" }, [
          vue.createElementVNode(
            "text",
            { class: "avatar-text" },
            vue.toDisplayString($setup.userInfo.username.charAt(0).toUpperCase()),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "user-info" }, [
          vue.createElementVNode(
            "text",
            { class: "username" },
            vue.toDisplayString($setup.userInfo.username),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "role" },
            vue.toDisplayString($setup.userInfo.roleText),
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 功能菜单 "),
      vue.createElementVNode("view", { class: "menu-section" }, [
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "📊"),
          vue.createElementVNode("text", { class: "menu-text" }, "工作统计"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "⚙️"),
          vue.createElementVNode("text", { class: "menu-text" }, "设置"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "❓"),
          vue.createElementVNode("text", { class: "menu-text" }, "帮助与反馈"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ]),
        vue.createElementVNode("view", { class: "menu-item" }, [
          vue.createElementVNode("view", { class: "menu-icon" }, "ℹ️"),
          vue.createElementVNode("text", { class: "menu-text" }, "关于我们"),
          vue.createElementVNode("text", { class: "menu-arrow" }, ">")
        ])
      ]),
      vue.createCommentVNode(" 退出登录 "),
      vue.createElementVNode("view", { class: "logout-section" }, [
        vue.createElementVNode("button", {
          class: "logout-btn",
          onClick: $setup.handleLogout
        }, " 退出登录 ")
      ])
    ]);
  }
  const PagesProfileProfile = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["render", _sfc_render$5], ["__file", "D:/learn/uniapptest/testvue3/src/pages/profile/profile.vue"]]);
  const __default__$2 = {
    onShow() {
      formatAppLog("log", "at pages/worker/home.vue:197", "织工页面显示，激活扫码功能");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(true);
      }
    },
    onHide() {
      formatAppLog("log", "at pages/worker/home.vue:204", "织工页面隐藏，停用扫码功能");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(false);
      }
    },
    onUnload() {
      formatAppLog("log", "at pages/worker/home.vue:210", "织工页面卸载，清理扫码资源");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.destroy();
      }
    }
  };
  const _sfc_main$5 = /* @__PURE__ */ vue.defineComponent({
    ...__default__$2,
    __name: "home",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({});
      const reportHistory = vue.ref([]);
      const isLoadingHistory = vue.ref(false);
      const getStatusInfo = (status) => {
        const statusMap = {
          "待维修": { text: "待维修", color: "#FF9800" },
          "维修中": { text: "维修中", color: "#2196F3" },
          "已完成": { text: "已完成", color: "#4CAF50" }
        };
        return statusMap[status] || { text: "未知", color: "#999999" };
      };
      const handleScanReport = async () => {
        try {
          const initResult = await scannerUtils.initScanner((result) => {
            if (result.success) {
              formatAppLog("log", "at pages/worker/home.vue:29", "扫码结果:", result);
              if (result.apiError) {
                uni.showToast({
                  title: result.apiError,
                  icon: "none",
                  duration: 2e3
                });
                uni.navigateTo({
                  url: `/pages/report/report?qrCode=${result.result}`
                });
              } else if (result.data) {
                if (result.data.can_report) {
                  uni.navigateTo({
                    url: `/pages/report/report?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data.machine))}`
                  });
                } else {
                  uni.showModal({
                    title: "提示",
                    content: result.data.message || "该机器已有未完成的异常报告",
                    showCancel: true,
                    cancelText: "取消",
                    confirmText: "查看详情",
                    success: (res) => {
                      if (res.confirm && result.data.pending_anomaly) {
                        uni.navigateTo({
                          url: `/pages/report/detail?id=${result.data.pending_anomaly.id}`
                        });
                      }
                    }
                  });
                }
              }
            } else {
              formatAppLog("error", "at pages/worker/home.vue:69", "扫码失败:", result.error);
              uni.showToast({
                title: result.error || "扫码失败",
                icon: "none"
              });
            }
          }, true, "report");
          if (initResult.success) {
            uni.showToast({
              title: "请扫描机器二维码",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:85", "初始化扫码失败:", error);
          uni.showToast({
            title: "扫码功能初始化失败",
            icon: "none"
          });
        }
      };
      const viewReportDetail = (report) => {
        uni.navigateTo({
          url: `/pages/report/detail?id=${report.id}`
        });
      };
      const loadReportHistory = async () => {
        try {
          isLoadingHistory.value = true;
          const response = await API.anomalies.getList({
            page: 1,
            page_size: 5
            // 只显示最近5条
          });
          const paginationData = ResponseHelper.getPagination(response);
          formatAppLog("log", "at pages/worker/home.vue:111", "paginationData", paginationData);
          if (paginationData.data) {
            reportHistory.value = paginationData.data.map((anomaly) => {
              var _a, _b;
              return {
                id: anomaly.id,
                machineCode: ((_a = anomaly.machine) == null ? void 0 : _a.code) || "",
                machineName: ((_b = anomaly.machine) == null ? void 0 : _b.name) || "",
                reportTime: new Date(anomaly.created_at).toLocaleString(),
                status: anomaly.status,
                description: anomaly.description
              };
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:123", "加载历史记录失败:", error);
          uni.showToast({
            title: error.message || "加载历史记录失败",
            icon: "none"
          });
        } finally {
          isLoadingHistory.value = false;
        }
      };
      const loadUserInfo = async () => {
        try {
          const response = await API.auth.getUserInfo();
          const userData = ResponseHelper.getData(response);
          if (userData) {
            userInfo.value = userData;
            uni.setStorageSync("userInfo", userData);
          }
        } catch (error) {
          formatAppLog("error", "at pages/worker/home.vue:146", "获取用户信息失败:", error);
          uni.reLaunch({
            url: "/pages/login/login"
          });
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.removeStorageSync("token");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        const user = uni.getStorageSync("userInfo");
        if (user) {
          userInfo.value = user;
        }
        await loadUserInfo();
        await loadReportHistory();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { userInfo, reportHistory, isLoadingHistory, getStatusInfo, handleScanReport, viewReportDetail, loadReportHistory, loadUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$4(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "worker-home" }, [
      vue.createCommentVNode(" 用户信息头部 "),
      vue.createTextVNode(" <"),
      vue.createCommentVNode(' view class="user-header">\n      <view class="user-info">\n        <view class="avatar">\n          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>\n        </view>\n        <view class="user-details">\n          <text class="username">{{ userInfo.username }}</text>\n          <text class="role">织工</text>\n        </view>\n      </view>\n      <view class="logout-btn" @click="handleLogout">\n        <text class="logout-text">退出</text>\n      </view>\n    </view> '),
      vue.createCommentVNode(" 快速操作区域 "),
      vue.createElementVNode("view", { class: "quick-actions" }, [
        vue.createElementVNode("view", {
          class: "scan-card",
          onClick: $setup.handleScanReport
        }, [
          vue.createElementVNode("view", { class: "scan-icon" }, [
            vue.createElementVNode("text", { class: "icon" }, "📱")
          ]),
          vue.createElementVNode("view", { class: "scan-content" }, [
            vue.createElementVNode("text", { class: "scan-title" }, "扫码上报异常"),
            vue.createElementVNode("text", { class: "scan-desc" }, "扫描机器二维码快速上报故障")
          ]),
          vue.createElementVNode("view", { class: "scan-arrow" }, [
            vue.createElementVNode("text", { class: "arrow" }, "→")
          ])
        ])
      ]),
      vue.createCommentVNode(" 统计信息 "),
      vue.createElementVNode("view", { class: "stats-section" }, [
        vue.createElementVNode("text", { class: "section-title" }, "今日统计"),
        vue.createElementVNode("view", { class: "stats-grid" }, [
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "3"),
            vue.createElementVNode("text", { class: "stat-label" }, "今日上报")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "1"),
            vue.createElementVNode("text", { class: "stat-label" }, "待维修")
          ]),
          vue.createElementVNode("view", { class: "stat-item" }, [
            vue.createElementVNode("text", { class: "stat-number" }, "2"),
            vue.createElementVNode("text", { class: "stat-label" }, "已完成")
          ])
        ])
      ]),
      vue.createCommentVNode(" 历史记录 "),
      vue.createElementVNode("view", { class: "history-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "最近上报"),
          vue.createElementVNode(
            "text",
            {
              class: "view-all",
              onClick: $setup.loadReportHistory
            },
            vue.toDisplayString($setup.isLoadingHistory ? "加载中..." : "刷新"),
            1
            /* TEXT */
          )
        ]),
        $setup.reportHistory.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "history-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.reportHistory, (item) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "history-item",
                key: item.id,
                onClick: ($event) => $setup.viewReportDetail(item)
              }, [
                vue.createElementVNode("view", { class: "machine-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "machine-name" },
                    vue.toDisplayString(item.machineName),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "text",
                    { class: "machine-code" },
                    vue.toDisplayString(item.machineCode),
                    1
                    /* TEXT */
                  )
                ]),
                vue.createElementVNode("view", { class: "report-info" }, [
                  vue.createElementVNode(
                    "text",
                    { class: "report-time" },
                    vue.toDisplayString(item.reportTime),
                    1
                    /* TEXT */
                  ),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-tag",
                      style: vue.normalizeStyle({ backgroundColor: $setup.getStatusInfo(item.status).color })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(item.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ])
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : !$setup.isLoadingHistory ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无上报记录"),
          vue.createElementVNode("text", { class: "empty-desc" }, "扫码上报机器异常后，记录将显示在这里")
        ])) : vue.createCommentVNode("v-if", true),
        $setup.isLoadingHistory ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "loading-state"
        }, [
          vue.createElementVNode("text", { class: "loading-text" }, "加载中...")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesWorkerHome = /* @__PURE__ */ _export_sfc(_sfc_main$5, [["render", _sfc_render$4], ["__file", "D:/learn/uniapptest/testvue3/src/pages/worker/home.vue"]]);
  const __default__$1 = {
    onShow() {
      formatAppLog("log", "at pages/mechanic/home.vue:236", "机修工页面显示，激活扫码功能");
      scannerUtils.setPageActive(true);
    },
    onHide() {
      formatAppLog("log", "at pages/mechanic/home.vue:240", "机修工页面隐藏，停用扫码功能");
      scannerUtils.setPageActive(false);
    },
    onUnload() {
      formatAppLog("log", "at pages/mechanic/home.vue:244", "机修工页面卸载，清理扫码资源");
      scannerUtils.destroy();
    }
  };
  const _sfc_main$4 = /* @__PURE__ */ vue.defineComponent({
    ...__default__$1,
    __name: "home",
    setup(__props, { expose: __expose }) {
      __expose();
      const userInfo = vue.ref({});
      const machineList = vue.ref([]);
      const isLoadingMachines = vue.ref(false);
      const stats = vue.ref({
        total: 0,
        normal: 0,
        warning: 0,
        error: 0,
        maintenance: 0
      });
      const getStatusInfo = (status) => {
        const statusMap = {
          "正常": { text: "正常", color: "#4CAF50", bgColor: "#E8F5E8" },
          "异常": { text: "异常", color: "#F44336", bgColor: "#FFEBEE" },
          "维修中": { text: "维修中", color: "#2196F3", bgColor: "#E3F2FD" }
        };
        return statusMap[status] || { text: "未知", color: "#999999", bgColor: "#F5F5F5" };
      };
      const handleScanRepair = async () => {
        try {
          const initResult = await scannerUtils.initScanner((result) => {
            if (result.success) {
              formatAppLog("log", "at pages/mechanic/home.vue:38", "扫码结果:", result);
              if (result.apiError) {
                uni.showToast({
                  title: result.apiError,
                  icon: "none",
                  duration: 2e3
                });
                uni.navigateTo({
                  url: `/pages/repair/repair?qrCode=${result.result}`
                });
              } else if (result.data) {
                const { machine, pending_anomalies, repairing_anomalies } = result.data;
                if (pending_anomalies.length > 0 || repairing_anomalies.length > 0) {
                  uni.navigateTo({
                    url: `/pages/repair/repair?qrCode=${result.result}&machineData=${encodeURIComponent(JSON.stringify(result.data))}`
                  });
                } else {
                  uni.showModal({
                    title: "提示",
                    content: `机器 ${machine.name} 当前状态正常，无需维修`,
                    showCancel: false
                  });
                }
              }
            } else {
              formatAppLog("error", "at pages/mechanic/home.vue:70", "扫码失败:", result.error);
              uni.showToast({
                title: result.error || "扫码失败",
                icon: "none"
              });
            }
          }, true, "repair");
          if (initResult.success) {
            uni.showToast({
              title: "请扫描机器二维码",
              icon: "none",
              duration: 2e3
            });
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:86", "初始化扫码失败:", error);
          uni.showToast({
            title: "扫码功能初始化失败",
            icon: "none"
          });
        }
      };
      const viewMachineDetail = (machine) => {
        const statusInfo = getStatusInfo(machine.status);
        let content = `位置：${machine.location}
状态：${statusInfo.text}`;
        if (machine.anomaly_count > 0) {
          content += `
异常数量：${machine.anomaly_count}个`;
        }
        uni.showModal({
          title: machine.name,
          content,
          showCancel: true,
          confirmText: "查看异常",
          cancelText: "关闭",
          success: (res) => {
            if (res.confirm && machine.anomaly_count > 0) {
              uni.navigateTo({
                url: `/pages/repair/list?machineId=${machine.id}`
              });
            }
          }
        });
      };
      const startRepair = (machine) => {
        uni.navigateTo({
          url: `/pages/repair/repair?machineId=${machine.id}&machineCode=${machine.code}`
        });
      };
      const loadMachineList = async () => {
        try {
          isLoadingMachines.value = true;
          const response = await API.machines.getList({
            page: 1,
            page_size: 50
            // 加载所有机器
          });
          const paginationData = ResponseHelper.getPagination(response);
          if (paginationData.data) {
            machineList.value = paginationData.data.map((machine) => ({
              ...machine,
              anomaly_count: parseInt(machine.qr_code) || 0
              // 后端临时将异常数量存在qr_code字段
            }));
            updateStats();
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:150", "加载机器列表失败:", error);
          uni.showToast({
            title: error.message || "加载机器列表失败",
            icon: "none"
          });
        } finally {
          isLoadingMachines.value = false;
        }
      };
      const updateStats = () => {
        const total = machineList.value.length;
        const normal = machineList.value.filter((m) => m.status === "正常").length;
        const error = machineList.value.filter((m) => m.status === "异常").length;
        const maintenance = machineList.value.filter((m) => m.status === "维修中").length;
        stats.value = {
          total,
          normal,
          warning: 0,
          // 暂时不使用警告状态
          error,
          maintenance
        };
      };
      const loadUserInfo = async () => {
        try {
          const response = await API.auth.getUserInfo();
          if (response) {
            userInfo.value = response;
            uni.setStorageSync("userInfo", response);
          }
        } catch (error) {
          formatAppLog("error", "at pages/mechanic/home.vue:185", "获取用户信息失败:", error);
          uni.reLaunch({
            url: "/pages/login/login"
          });
        }
      };
      const handleLogout = () => {
        uni.showModal({
          title: "确认退出",
          content: "确定要退出登录吗？",
          success: (res) => {
            if (res.confirm) {
              uni.removeStorageSync("userInfo");
              uni.removeStorageSync("token");
              uni.reLaunch({
                url: "/pages/login/login"
              });
            }
          }
        });
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        const user = uni.getStorageSync("userInfo");
        if (user) {
          userInfo.value = user;
        }
        await loadUserInfo();
        await loadMachineList();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { userInfo, machineList, isLoadingMachines, stats, getStatusInfo, handleScanRepair, viewMachineDetail, startRepair, loadMachineList, updateStats, loadUserInfo, handleLogout };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$3(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "mechanic-home" }, [
      vue.createCommentVNode(" 用户信息头部 "),
      vue.createCommentVNode(' <view class="user-header">\n      <view class="user-info">\n        <view class="avatar">\n          <text class="avatar-text">{{ userInfo.username?.charAt(0)?.toUpperCase() }}</text>\n        </view>\n        <view class="user-details">\n          <text class="username">{{ userInfo.username }}</text>\n          <text class="role">机修工</text>\n        </view>\n      </view>\n      <view class="logout-btn" @click="handleLogout">\n        <text class="logout-text">退出</text>\n      </view>\n    </view> '),
      vue.createCommentVNode(" 扫码维修提示 "),
      vue.createElementVNode("view", {
        class: "scan-tip",
        onClick: $setup.handleScanRepair
      }, [
        vue.createElementVNode("view", { class: "scan-icon" }, [
          vue.createElementVNode("text", { class: "icon" }, "📱")
        ]),
        vue.createElementVNode("view", { class: "scan-text" }, [
          vue.createElementVNode("text", { class: "tip-title" }, "扫码开始维修"),
          vue.createElementVNode("text", { class: "tip-desc" }, "扫描机器二维码直接进入维修页面")
        ]),
        vue.createElementVNode("view", { class: "scan-arrow" }, [
          vue.createElementVNode("text", { class: "arrow" }, "→")
        ])
      ]),
      vue.createCommentVNode(" 统计概览 "),
      vue.createElementVNode("view", { class: "stats-overview" }, [
        vue.createElementVNode("text", { class: "section-title" }, "设备状态概览"),
        vue.createElementVNode("view", { class: "stats-grid" }, [
          vue.createElementVNode("view", { class: "stat-card" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.total),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "总设备")
          ]),
          vue.createElementVNode("view", { class: "stat-card normal" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.normal),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "正常")
          ]),
          vue.createElementVNode("view", { class: "stat-card warning" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.warning),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "警告")
          ]),
          vue.createElementVNode("view", { class: "stat-card error" }, [
            vue.createElementVNode(
              "text",
              { class: "stat-number" },
              vue.toDisplayString($setup.stats.error),
              1
              /* TEXT */
            ),
            vue.createElementVNode("text", { class: "stat-label" }, "故障")
          ])
        ])
      ]),
      vue.createCommentVNode(" 机器列表 "),
      vue.createElementVNode("view", { class: "machine-section" }, [
        vue.createElementVNode("view", { class: "section-header" }, [
          vue.createElementVNode("text", { class: "section-title" }, "设备列表"),
          vue.createElementVNode(
            "text",
            {
              class: "refresh-btn",
              onClick: $setup.loadMachineList
            },
            vue.toDisplayString($setup.isLoadingMachines ? "加载中..." : "刷新"),
            1
            /* TEXT */
          )
        ]),
        $setup.machineList.length > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "machine-list"
        }, [
          (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            null,
            vue.renderList($setup.machineList, (machine) => {
              return vue.openBlock(), vue.createElementBlock("view", {
                class: "machine-card",
                key: machine.id,
                onClick: ($event) => $setup.viewMachineDetail(machine)
              }, [
                vue.createElementVNode("view", { class: "machine-header" }, [
                  vue.createElementVNode("view", { class: "machine-info" }, [
                    vue.createElementVNode(
                      "text",
                      { class: "machine-name" },
                      vue.toDisplayString(machine.name),
                      1
                      /* TEXT */
                    ),
                    vue.createElementVNode(
                      "text",
                      { class: "machine-location" },
                      vue.toDisplayString(machine.location),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode(
                    "view",
                    {
                      class: "status-badge",
                      style: vue.normalizeStyle({
                        backgroundColor: $setup.getStatusInfo(machine.status).bgColor,
                        color: $setup.getStatusInfo(machine.status).color
                      })
                    },
                    [
                      vue.createElementVNode(
                        "text",
                        { class: "status-text" },
                        vue.toDisplayString($setup.getStatusInfo(machine.status).text),
                        1
                        /* TEXT */
                      )
                    ],
                    4
                    /* STYLE */
                  )
                ]),
                vue.createElementVNode("view", { class: "machine-metrics" }, [
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "编号"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString(machine.code),
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "异常数量"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString(machine.anomaly_count) + "个",
                      1
                      /* TEXT */
                    )
                  ]),
                  vue.createElementVNode("view", { class: "metric-item" }, [
                    vue.createElementVNode("text", { class: "metric-label" }, "状态"),
                    vue.createElementVNode(
                      "text",
                      { class: "metric-value" },
                      vue.toDisplayString($setup.getStatusInfo(machine.status).text),
                      1
                      /* TEXT */
                    )
                  ])
                ]),
                machine.status === "异常" && machine.anomaly_count > 0 ? (vue.openBlock(), vue.createElementBlock("view", {
                  key: 0,
                  class: "machine-actions"
                }, [
                  vue.createElementVNode("button", {
                    class: "repair-btn",
                    onClick: vue.withModifiers(($event) => $setup.startRepair(machine), ["stop"])
                  }, " 开始维修 ", 8, ["onClick"])
                ])) : vue.createCommentVNode("v-if", true)
              ], 8, ["onClick"]);
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ])) : !$setup.isLoadingMachines ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "empty-state"
        }, [
          vue.createElementVNode("text", { class: "empty-text" }, "暂无设备数据"),
          vue.createElementVNode("text", { class: "empty-desc" }, "请检查网络连接或联系管理员")
        ])) : vue.createCommentVNode("v-if", true),
        $setup.isLoadingMachines ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 2,
          class: "loading-state"
        }, [
          vue.createElementVNode("text", { class: "loading-text" }, "加载设备列表中...")
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesMechanicHome = /* @__PURE__ */ _export_sfc(_sfc_main$4, [["render", _sfc_render$3], ["__file", "D:/learn/uniapptest/testvue3/src/pages/mechanic/home.vue"]]);
  const _sfc_main$3 = /* @__PURE__ */ vue.defineComponent({
    __name: "report",
    setup(__props, { expose: __expose }) {
      __expose();
      const machineInfo = vue.ref({
        code: "",
        name: "",
        location: "",
        model: "",
        id: null
      });
      const reportForm = vue.reactive({
        machineCode: "",
        description: "",
        severity: "medium",
        // low: 轻微, medium: 中等, high: 严重, critical: 紧急
        images: [],
        remarks: ""
      });
      const severityOptions = [
        { value: "low", label: "轻微", color: "#4CAF50" },
        { value: "medium", label: "中等", color: "#FF9800" },
        { value: "high", label: "严重", color: "#F44336" },
        { value: "critical", label: "紧急", color: "#9C27B0" }
      ];
      const isSubmitting = vue.ref(false);
      const mockMachineData = {
        "M001": {
          id: 1,
          code: "M001",
          name: "织机A-01",
          location: "A区-1号位",
          model: "TX-2000",
          installDate: "2023-03-15",
          lastMaintenance: "2024-01-10"
        },
        "M002": {
          id: 2,
          code: "M002",
          name: "织机B-03",
          location: "B区-3号位",
          model: "TX-2000",
          installDate: "2023-04-20",
          lastMaintenance: "2024-01-12"
        },
        "M003": {
          id: 3,
          code: "M003",
          name: "织机C-05",
          location: "C区-5号位",
          model: "TX-3000",
          installDate: "2023-05-10",
          lastMaintenance: "2024-01-14"
        }
      };
      const getSeverityInfo = (severity) => {
        return severityOptions.find((item) => item.value === severity) || severityOptions[1];
      };
      const onSeverityChange = (e) => {
        reportForm.severity = e.detail.value;
      };
      const chooseImage = () => {
        uni.chooseImage({
          count: 3,
          sizeType: ["compressed"],
          sourceType: ["camera", "album"],
          success: (res) => {
            reportForm.images = [...reportForm.images, ...res.tempFilePaths].slice(0, 3);
          },
          fail: (err) => {
            formatAppLog("error", "at pages/report/report.vue:86", "选择图片失败:", err);
            uni.showToast({
              title: "选择图片失败",
              icon: "none"
            });
          }
        });
      };
      const removeImage = (index) => {
        reportForm.images.splice(index, 1);
      };
      const previewImage = (index) => {
        uni.previewImage({
          urls: reportForm.images,
          current: index
        });
      };
      const getMachineInfo = async (codeOrQrCode) => {
        try {
          let response;
          if (codeOrQrCode.startsWith("QR_")) {
            response = await API.machines.getByQRCode(codeOrQrCode);
          } else {
            response = await API.machines.getByCode(codeOrQrCode);
          }
          const machineData = ResponseHelper.getData(response);
          if (machineData && machineData.machine) {
            machineInfo.value = machineData.machine;
          } else {
            throw new Error("未找到机器信息");
          }
        } catch (error) {
          formatAppLog("error", "at pages/report/report.vue:129", "获取机器信息失败:", error);
          const machineCode = codeOrQrCode.replace("QR_", "");
          const machine = mockMachineData[machineCode];
          if (machine) {
            machineInfo.value = machine;
          } else {
            machineInfo.value = {
              id: 1,
              // 默认ID
              code: machineCode,
              name: `机器-${machineCode}`,
              location: "未知位置",
              model: "未知型号"
            };
          }
        }
      };
      const submitReport = async () => {
        formatAppLog("log", "at pages/report/report.vue:149", "开始提交异常上报");
        formatAppLog("log", "at pages/report/report.vue:150", "machineInfo:", machineInfo.value);
        formatAppLog("log", "at pages/report/report.vue:151", "reportForm:", reportForm);
        if (!reportForm.description.trim()) {
          uni.showToast({
            title: "请填写异常描述",
            icon: "none"
          });
          return;
        }
        if (!machineInfo.value.id) {
          formatAppLog("error", "at pages/report/report.vue:162", "机器ID缺失:", machineInfo.value);
          uni.showToast({
            title: "机器信息获取失败，请重试",
            icon: "none"
          });
          return;
        }
        if (!reportForm.severity) {
          formatAppLog("error", "at pages/report/report.vue:171", "严重程度缺失:", reportForm.severity);
          uni.showToast({
            title: "请选择严重程度",
            icon: "none"
          });
          return;
        }
        isSubmitting.value = true;
        try {
          const reportData = {
            machine_id: machineInfo.value.id,
            description: reportForm.description.trim(),
            severity: reportForm.severity,
            remark: reportForm.remarks.trim() || ""
          };
          formatAppLog("log", "at pages/report/report.vue:190", "准备发送的上报数据:", reportData);
          if (!reportData.machine_id || !reportData.description || !reportData.severity) {
            throw new Error("数据不完整：" + JSON.stringify({
              machine_id: reportData.machine_id,
              description: reportData.description,
              severity: reportData.severity
            }));
          }
          const response = await API.anomalies.create(reportData);
          formatAppLog("log", "at pages/report/report.vue:204", "API响应:", response);
          if (ResponseHelper.isSuccess(response)) {
            uni.showToast({
              title: "上报成功",
              icon: "success"
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1500);
          } else {
            throw new Error(ResponseHelper.getErrorMessage(response));
          }
        } catch (error) {
          formatAppLog("error", "at pages/report/report.vue:221", "上报失败详细信息:", error);
          uni.showToast({
            title: error.message || "上报失败，请重试",
            icon: "none"
          });
        } finally {
          isSubmitting.value = false;
        }
      };
      onLoad((options) => {
        formatAppLog("log", "at pages/report/report.vue:233", "onLoad options:", options);
        const machineCode = options.machineCode || options.qrCode;
        if (machineCode) {
          reportForm.machineCode = machineCode;
          getMachineInfo(machineCode);
        } else {
          uni.showToast({
            title: "缺少机器信息",
            icon: "none"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
      const __returned__ = { machineInfo, reportForm, severityOptions, isSubmitting, mockMachineData, getSeverityInfo, onSeverityChange, chooseImage, removeImage, previewImage, getMachineInfo, submitReport };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$2(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "report-page" }, [
      vue.createCommentVNode(" 机器信息卡片 "),
      vue.createElementVNode("view", { class: "machine-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "机器信息"),
          vue.createElementVNode("view", { class: "machine-code" }, [
            vue.createElementVNode(
              "text",
              { class: "code-text" },
              vue.toDisplayString($setup.machineInfo.code || "加载中..."),
              1
              /* TEXT */
            )
          ])
        ]),
        vue.createElementVNode("view", { class: "machine-details" }, [
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备名称："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.name || "加载中..."),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备位置："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.location || "加载中..."),
              1
              /* TEXT */
            )
          ]),
          $setup.machineInfo.model ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备型号："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.model),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true),
          $setup.machineInfo.lastMaintenance ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 1,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "上次维护："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.lastMaintenance),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true)
        ])
      ]),
      vue.createCommentVNode(" 异常上报表单 "),
      vue.createElementVNode("view", { class: "form-container" }, [
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "异常描述 *"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "description-input",
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.reportForm.description = $event),
              placeholder: "请详细描述机器异常现象，如异响、温度异常、运行不稳定等",
              "placeholder-class": "placeholder",
              maxlength: "500",
              "show-confirm-bar": "false"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.reportForm.description]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.reportForm.description.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "严重程度"),
          vue.createElementVNode("picker", {
            class: "severity-picker",
            value: $setup.reportForm.severity,
            range: $setup.severityOptions,
            "range-key": "label",
            onChange: $setup.onSeverityChange
          }, [
            vue.createElementVNode("view", { class: "picker-content" }, [
              vue.createElementVNode(
                "view",
                {
                  class: "severity-tag",
                  style: vue.normalizeStyle({ backgroundColor: $setup.getSeverityInfo($setup.reportForm.severity).color })
                },
                [
                  vue.createElementVNode(
                    "text",
                    { class: "severity-text" },
                    vue.toDisplayString($setup.getSeverityInfo($setup.reportForm.severity).label),
                    1
                    /* TEXT */
                  )
                ],
                4
                /* STYLE */
              ),
              vue.createElementVNode("text", { class: "picker-arrow" }, "▼")
            ])
          ], 40, ["value"])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "现场照片 (最多3张)"),
          vue.createElementVNode("view", { class: "image-section" }, [
            vue.createElementVNode("view", { class: "image-list" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.reportForm.images, (image, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    class: "image-item",
                    key: index,
                    onClick: ($event) => $setup.previewImage(index)
                  }, [
                    vue.createElementVNode("image", {
                      class: "image",
                      src: image,
                      mode: "aspectFill"
                    }, null, 8, ["src"]),
                    vue.createElementVNode("view", {
                      class: "image-remove",
                      onClick: vue.withModifiers(($event) => $setup.removeImage(index), ["stop"])
                    }, [
                      vue.createElementVNode("text", { class: "remove-icon" }, "×")
                    ], 8, ["onClick"])
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              )),
              $setup.reportForm.images.length < 3 ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "image-add",
                onClick: $setup.chooseImage
              }, [
                vue.createElementVNode("text", { class: "add-icon" }, "+"),
                vue.createElementVNode("text", { class: "add-text" }, "添加照片")
              ])) : vue.createCommentVNode("v-if", true)
            ])
          ])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "remarks-input",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.reportForm.remarks = $event),
              placeholder: "其他需要说明的信息（选填）",
              "placeholder-class": "placeholder",
              maxlength: "200",
              "show-confirm-bar": "false"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $setup.reportForm.remarks]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.reportForm.remarks.length) + "/200",
            1
            /* TEXT */
          )
        ])
      ]),
      vue.createCommentVNode(" 提交按钮 "),
      vue.createElementVNode("view", { class: "submit-section" }, [
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["submit-btn", { "submitting": $setup.isSubmitting }]),
          onClick: $setup.submitReport,
          disabled: $setup.isSubmitting
        }, vue.toDisplayString($setup.isSubmitting ? "提交中..." : "提交异常上报"), 11, ["disabled"])
      ])
    ]);
  }
  const PagesReportReport = /* @__PURE__ */ _export_sfc(_sfc_main$3, [["render", _sfc_render$2], ["__file", "D:/learn/uniapptest/testvue3/src/pages/report/report.vue"]]);
  const _sfc_main$2 = /* @__PURE__ */ vue.defineComponent({
    __name: "repair",
    setup(__props, { expose: __expose }) {
      __expose();
      const machineInfo = vue.ref({});
      const errorInfo = vue.ref({});
      const repairForm = vue.reactive({
        startTime: "",
        endTime: "",
        repairProcess: "",
        replacedParts: "",
        repairResult: "success",
        // success: 维修成功, failed: 维修失败, pending: 需要配件
        testResult: "",
        remarks: "",
        images: []
      });
      const repairStatus = vue.ref("not_started");
      const isSubmitting = vue.ref(false);
      const repairResultOptions = [
        { value: "success", label: "维修成功", color: "#4CAF50" },
        { value: "failed", label: "维修失败", color: "#F44336" },
        { value: "pending", label: "需要配件", color: "#FF9800" }
      ];
      const mockMachineData = {
        "M001": {
          code: "M001",
          name: "织机A-01",
          location: "A区-1号位",
          model: "TX-2000",
          installDate: "2023-03-15",
          lastMaintenance: "2024-01-10"
        },
        "M002": {
          code: "M002",
          name: "织机B-03",
          location: "B区-3号位",
          model: "TX-2000",
          installDate: "2023-04-20",
          lastMaintenance: "2024-01-12"
        },
        "M003": {
          code: "M003",
          name: "织机C-05",
          location: "C区-5号位",
          model: "TX-3000",
          installDate: "2023-05-10",
          lastMaintenance: "2024-01-14"
        }
      };
      const mockErrorData = {
        "M001": {
          id: 1,
          description: "机器异响，疑似轴承问题",
          severity: "high",
          reportTime: "2024-01-15 14:30",
          reporter: "张三",
          images: ["/static/error1.jpg"]
        },
        "M003": {
          id: 3,
          description: "温度过高，需要检查冷却系统",
          severity: "critical",
          reportTime: "2024-01-14 16:45",
          reporter: "李四",
          images: []
        }
      };
      const getSeverityInfo = (severity) => {
        const severityMap = {
          low: { text: "轻微", color: "#4CAF50" },
          medium: { text: "中等", color: "#FF9800" },
          high: { text: "严重", color: "#F44336" },
          critical: { text: "紧急", color: "#9C27B0" }
        };
        return severityMap[severity] || { text: "未知", color: "#999999" };
      };
      const getRepairResultInfo = (result) => {
        return repairResultOptions.find((item) => item.value === result) || repairResultOptions[0];
      };
      const startRepair = () => {
        const now = /* @__PURE__ */ new Date();
        repairForm.startTime = now.toLocaleString();
        repairStatus.value = "in_progress";
        uni.showToast({
          title: "开始维修",
          icon: "success"
        });
      };
      const onRepairResultChange = (e) => {
        repairForm.repairResult = e.detail.value;
      };
      const chooseImage = () => {
        uni.chooseImage({
          count: 5,
          sizeType: ["compressed"],
          sourceType: ["camera", "album"],
          success: (res) => {
            repairForm.images = [...repairForm.images, ...res.tempFilePaths].slice(0, 5);
          },
          fail: (err) => {
            formatAppLog("error", "at pages/repair/repair.vue:124", "选择图片失败:", err);
            uni.showToast({
              title: "选择图片失败",
              icon: "none"
            });
          }
        });
      };
      const removeImage = (index) => {
        repairForm.images.splice(index, 1);
      };
      const previewImage = (index) => {
        uni.previewImage({
          urls: repairForm.images,
          current: index
        });
      };
      const completeRepair = async () => {
        var _a;
        if (!repairForm.repairProcess.trim()) {
          uni.showToast({
            title: "请填写维修过程",
            icon: "none"
          });
          return;
        }
        isSubmitting.value = true;
        try {
          const now = /* @__PURE__ */ new Date();
          repairForm.endTime = now.toLocaleString();
          await new Promise((resolve) => setTimeout(resolve, 2e3));
          const repairData = {
            machineCode: machineInfo.value.code,
            machineName: machineInfo.value.name,
            errorId: errorInfo.value.id,
            startTime: repairForm.startTime,
            endTime: repairForm.endTime,
            repairProcess: repairForm.repairProcess,
            replacedParts: repairForm.replacedParts,
            repairResult: repairForm.repairResult,
            testResult: repairForm.testResult,
            remarks: repairForm.remarks,
            images: repairForm.images,
            mechanic: ((_a = uni.getStorageSync("userInfo")) == null ? void 0 : _a.username) || "未知维修工"
          };
          formatAppLog("log", "at pages/repair/repair.vue:182", "维修数据:", repairData);
          repairStatus.value = "completed";
          uni.showToast({
            title: "维修完成",
            icon: "success"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          formatAppLog("error", "at pages/repair/repair.vue:197", "提交失败:", error);
          uni.showToast({
            title: "提交失败，请重试",
            icon: "none"
          });
        } finally {
          isSubmitting.value = false;
        }
      };
      vue.onLoad((options) => {
        if (options.machineCode) {
          const machine = mockMachineData[options.machineCode];
          if (machine) {
            machineInfo.value = machine;
          } else {
            machineInfo.value = {
              code: options.machineCode,
              name: `机器-${options.machineCode}`,
              location: "未知位置",
              model: "未知型号"
            };
          }
          const error = mockErrorData[options.machineCode];
          if (error) {
            errorInfo.value = error;
          } else {
            errorInfo.value = {
              id: 0,
              description: "暂无异常描述",
              severity: "medium",
              reportTime: "未知时间",
              reporter: "未知上报人",
              images: []
            };
          }
        } else {
          uni.showToast({
            title: "缺少机器信息",
            icon: "none"
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      });
      const __returned__ = { machineInfo, errorInfo, repairForm, repairStatus, isSubmitting, repairResultOptions, mockMachineData, mockErrorData, getSeverityInfo, getRepairResultInfo, startRepair, onRepairResultChange, chooseImage, removeImage, previewImage, completeRepair };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "repair-page" }, [
      vue.createCommentVNode(" 机器信息卡片 "),
      vue.createElementVNode("view", { class: "machine-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "设备信息"),
          vue.createElementVNode("view", { class: "machine-code" }, [
            vue.createElementVNode(
              "text",
              { class: "code-text" },
              vue.toDisplayString($setup.machineInfo.code),
              1
              /* TEXT */
            )
          ])
        ]),
        vue.createElementVNode("view", { class: "machine-details" }, [
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备名称："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.name),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "detail-row" }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备位置："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.location),
              1
              /* TEXT */
            )
          ]),
          $setup.machineInfo.model ? (vue.openBlock(), vue.createElementBlock("view", {
            key: 0,
            class: "detail-row"
          }, [
            vue.createElementVNode("text", { class: "detail-label" }, "设备型号："),
            vue.createElementVNode(
              "text",
              { class: "detail-value" },
              vue.toDisplayString($setup.machineInfo.model),
              1
              /* TEXT */
            )
          ])) : vue.createCommentVNode("v-if", true)
        ])
      ]),
      vue.createCommentVNode(" 异常信息卡片 "),
      vue.createElementVNode("view", { class: "error-card" }, [
        vue.createElementVNode("view", { class: "card-header" }, [
          vue.createElementVNode("text", { class: "card-title" }, "异常信息"),
          vue.createElementVNode(
            "view",
            {
              class: "severity-badge",
              style: vue.normalizeStyle({ backgroundColor: $setup.getSeverityInfo($setup.errorInfo.severity).color })
            },
            [
              vue.createElementVNode(
                "text",
                { class: "severity-text" },
                vue.toDisplayString($setup.getSeverityInfo($setup.errorInfo.severity).text),
                1
                /* TEXT */
              )
            ],
            4
            /* STYLE */
          )
        ]),
        vue.createElementVNode("view", { class: "error-details" }, [
          vue.createElementVNode("view", { class: "error-desc" }, [
            vue.createElementVNode(
              "text",
              { class: "desc-text" },
              vue.toDisplayString($setup.errorInfo.description),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "error-meta" }, [
            vue.createElementVNode(
              "text",
              { class: "meta-item" },
              "上报时间：" + vue.toDisplayString($setup.errorInfo.reportTime),
              1
              /* TEXT */
            ),
            vue.createElementVNode(
              "text",
              { class: "meta-item" },
              "上报人：" + vue.toDisplayString($setup.errorInfo.reporter),
              1
              /* TEXT */
            )
          ])
        ])
      ]),
      vue.createCommentVNode(" 维修状态 "),
      vue.createElementVNode("view", { class: "status-card" }, [
        vue.createElementVNode("view", { class: "status-header" }, [
          vue.createElementVNode("text", { class: "status-title" }, "维修状态"),
          vue.createElementVNode(
            "view",
            {
              class: vue.normalizeClass(["status-badge", $setup.repairStatus])
            },
            [
              vue.createElementVNode(
                "text",
                { class: "status-text" },
                vue.toDisplayString($setup.repairStatus === "not_started" ? "未开始" : $setup.repairStatus === "in_progress" ? "进行中" : "已完成"),
                1
                /* TEXT */
              )
            ],
            2
            /* CLASS */
          )
        ]),
        $setup.repairForm.startTime ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "time-info"
        }, [
          vue.createElementVNode(
            "text",
            { class: "time-item" },
            "开始时间：" + vue.toDisplayString($setup.repairForm.startTime),
            1
            /* TEXT */
          ),
          $setup.repairForm.endTime ? (vue.openBlock(), vue.createElementBlock(
            "text",
            {
              key: 0,
              class: "time-item"
            },
            "结束时间：" + vue.toDisplayString($setup.repairForm.endTime),
            1
            /* TEXT */
          )) : vue.createCommentVNode("v-if", true)
        ])) : vue.createCommentVNode("v-if", true),
        $setup.repairStatus === "not_started" ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 1,
          class: "action-section"
        }, [
          vue.createElementVNode("button", {
            class: "start-btn",
            onClick: $setup.startRepair
          }, " 开始维修 ")
        ])) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 维修表单 "),
      $setup.repairStatus !== "not_started" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "form-container"
      }, [
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修过程 *"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "process-input",
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $setup.repairForm.repairProcess = $event),
            placeholder: "请详细记录维修过程，包括故障原因分析、维修步骤等",
            "placeholder-class": "placeholder",
            maxlength: "1000",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.repairProcess]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.repairProcess.length) + "/1000",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "更换配件"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "parts-input",
            "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $setup.repairForm.replacedParts = $event),
            placeholder: "记录更换的配件名称、型号、数量等（如无更换可不填）",
            "placeholder-class": "placeholder",
            maxlength: "500",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.replacedParts]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.replacedParts.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修结果"),
          vue.createElementVNode("picker", {
            class: "result-picker",
            value: $setup.repairForm.repairResult,
            range: $setup.repairResultOptions,
            "range-key": "label",
            onChange: $setup.onRepairResultChange,
            disabled: $setup.repairStatus === "completed"
          }, [
            vue.createElementVNode("view", { class: "picker-content" }, [
              vue.createElementVNode(
                "view",
                {
                  class: "result-tag",
                  style: vue.normalizeStyle({ backgroundColor: $setup.getRepairResultInfo($setup.repairForm.repairResult).color })
                },
                [
                  vue.createElementVNode(
                    "text",
                    { class: "result-text" },
                    vue.toDisplayString($setup.getRepairResultInfo($setup.repairForm.repairResult).label),
                    1
                    /* TEXT */
                  )
                ],
                4
                /* STYLE */
              ),
              $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("text", {
                key: 0,
                class: "picker-arrow"
              }, "▼")) : vue.createCommentVNode("v-if", true)
            ])
          ], 40, ["value", "disabled"])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "测试结果"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "test-input",
            "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => $setup.repairForm.testResult = $event),
            placeholder: "记录维修后的测试情况，如运行状态、性能指标等",
            "placeholder-class": "placeholder",
            maxlength: "500",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.testResult]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.testResult.length) + "/500",
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "维修照片 (最多5张)"),
          vue.createElementVNode("view", { class: "image-section" }, [
            vue.createElementVNode("view", { class: "image-list" }, [
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList($setup.repairForm.images, (image, index) => {
                  return vue.openBlock(), vue.createElementBlock("view", {
                    class: "image-item",
                    key: index,
                    onClick: ($event) => $setup.previewImage(index)
                  }, [
                    vue.createElementVNode("image", {
                      class: "image",
                      src: image,
                      mode: "aspectFill"
                    }, null, 8, ["src"]),
                    $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("view", {
                      key: 0,
                      class: "image-remove",
                      onClick: vue.withModifiers(($event) => $setup.removeImage(index), ["stop"])
                    }, [
                      vue.createElementVNode("text", { class: "remove-icon" }, "×")
                    ], 8, ["onClick"])) : vue.createCommentVNode("v-if", true)
                  ], 8, ["onClick"]);
                }),
                128
                /* KEYED_FRAGMENT */
              )),
              $setup.repairForm.images.length < 5 && $setup.repairStatus !== "completed" ? (vue.openBlock(), vue.createElementBlock("view", {
                key: 0,
                class: "image-add",
                onClick: $setup.chooseImage
              }, [
                vue.createElementVNode("text", { class: "add-icon" }, "+"),
                vue.createElementVNode("text", { class: "add-text" }, "添加照片")
              ])) : vue.createCommentVNode("v-if", true)
            ])
          ])
        ]),
        vue.createElementVNode("view", { class: "form-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "备注信息"),
          vue.withDirectives(vue.createElementVNode("textarea", {
            class: "remarks-input",
            "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => $setup.repairForm.remarks = $event),
            placeholder: "其他需要说明的信息（选填）",
            "placeholder-class": "placeholder",
            maxlength: "300",
            "show-confirm-bar": "false",
            disabled: $setup.repairStatus === "completed"
          }, null, 8, ["disabled"]), [
            [vue.vModelText, $setup.repairForm.remarks]
          ]),
          vue.createElementVNode(
            "text",
            { class: "char-count" },
            vue.toDisplayString($setup.repairForm.remarks.length) + "/300",
            1
            /* TEXT */
          )
        ])
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 完成按钮 "),
      $setup.repairStatus === "in_progress" ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 1,
        class: "submit-section"
      }, [
        vue.createElementVNode("button", {
          class: vue.normalizeClass(["complete-btn", { "submitting": $setup.isSubmitting }]),
          onClick: $setup.completeRepair,
          disabled: $setup.isSubmitting
        }, vue.toDisplayString($setup.isSubmitting ? "提交中..." : "完成维修"), 11, ["disabled"])
      ])) : vue.createCommentVNode("v-if", true)
    ]);
  }
  const PagesRepairRepair = /* @__PURE__ */ _export_sfc(_sfc_main$2, [["render", _sfc_render$1], ["__file", "D:/learn/uniapptest/testvue3/src/pages/repair/repair.vue"]]);
  class SunmiConfig {
    /**
     * 检查是否为商米设备
     */
    static isSunmiDevice() {
      if (uni.getSystemInfoSync().platform !== "android") {
        return false;
      }
      try {
        const systemInfo = uni.getSystemInfoSync();
        if (systemInfo.brand && systemInfo.brand.toLowerCase().includes("sunmi")) {
          return true;
        }
        const main = plus.android.runtimeMainActivity();
        const packageManager = main.getPackageManager();
        const PackageManager = plus.android.importClass("android.content.pm.PackageManager");
        try {
          const packageInfo = packageManager.getPackageInfo(this.SERVICE_PACKAGE, 0);
          return packageInfo != null;
        } catch (packageError) {
          formatAppLog("log", "at utils/sunmiConfig.js:64", "商米扫码服务包不存在:", packageError);
          return false;
        }
      } catch (error) {
        formatAppLog("log", "at utils/sunmiConfig.js:68", "检查商米设备失败:", error);
        return false;
      }
    }
    /**
     * 获取设备信息
     */
    static getDeviceInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        return {
          platform: systemInfo.platform,
          brand: systemInfo.brand,
          model: systemInfo.model,
          system: systemInfo.system
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:86", "获取设备信息失败:", error);
        return null;
      }
    }
    /**
     * 配置扫码输出方式
     * 根据商米文档，设置为广播输出模式
     */
    static async configureOutputMode() {
      try {
        formatAppLog("log", "at utils/sunmiConfig.js:99", "商米扫码头输出配置提示：");
        formatAppLog("log", "at utils/sunmiConfig.js:100", "1. 打开设备设置 -> 扫码头设置");
        formatAppLog("log", "at utils/sunmiConfig.js:101", '2. 确保"广播输出"已开启');
        formatAppLog("log", "at utils/sunmiConfig.js:102", "3. 确保监听广播为: " + this.BROADCAST_ACTION);
        return {
          success: true,
          message: "请确认设备扫码配置已正确设置"
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:109", "配置扫码输出模式失败:", error);
        return {
          success: false,
          error: error.message
        };
      }
    }
    /**
     * 检查扫码头配置
     */
    static async checkScannerConfig() {
      if (!this.isSunmiDevice()) {
        return {
          success: false,
          error: "非商米设备或扫码服务不可用"
        };
      }
      try {
        const deviceInfo = this.getDeviceInfo();
        formatAppLog("log", "at utils/sunmiConfig.js:130", "设备信息:", deviceInfo);
        return {
          success: true,
          deviceInfo,
          message: "商米扫码头检查通过"
        };
      } catch (error) {
        formatAppLog("error", "at utils/sunmiConfig.js:138", "检查扫码头配置失败:", error);
        return {
          success: false,
          error: error.message
        };
      }
    }
  }
  /**
   * 扫码头类型常量
   */
  __publicField(SunmiConfig, "SCANNER_MODELS", {
    NONE: 100,
    P2LITE_V2PRO_P2PRO: 101,
    L2_NEWLAND_EM2096: 102,
    L2_ZEBRA_SE4710: 103,
    L2_HONEYWELL_N3601: 104,
    L2_HONEYWELL_N6603: 105,
    L2_ZEBRA_SE4750: 106,
    L2_ZEBRA_EM1350: 107
  });
  /**
   * 广播动作常量
   */
  __publicField(SunmiConfig, "BROADCAST_ACTION", "com.sunmi.scanner.ACTION_DATA_CODE_RECEIVED");
  /**
   * 扫码服务相关常量
   */
  __publicField(SunmiConfig, "SERVICE_PACKAGE", "com.sunmi.scanner");
  __publicField(SunmiConfig, "SERVICE_ACTION", "com.sunmi.scanner.IScanInterface");
  /**
   * 数据字段常量
   */
  __publicField(SunmiConfig, "DATA_FIELD", "data");
  __publicField(SunmiConfig, "SOURCE_BYTE_FIELD", "source_byte");
  const __default__ = {
    onShow() {
      formatAppLog("log", "at pages/test/scanner.vue:175", "扫码测试页面显示");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(true);
      }
    },
    onHide() {
      formatAppLog("log", "at pages/test/scanner.vue:181", "扫码测试页面隐藏");
      if (typeof scannerUtils !== "undefined") {
        scannerUtils.setPageActive(false);
      }
    }
  };
  const _sfc_main$1 = /* @__PURE__ */ vue.defineComponent({
    ...__default__,
    __name: "scanner",
    setup(__props, { expose: __expose }) {
      __expose();
      const scanResult = vue.ref("");
      const scanStatus = vue.ref("未初始化");
      const isScanning = vue.ref(false);
      const deviceInfo = vue.ref({});
      const scannerType = vue.ref("未知");
      const handleScanResult = (result) => {
        isScanning.value = false;
        if (result.success) {
          scanResult.value = result.result;
          scanStatus.value = "扫码成功";
          let message = `扫码结果: ${result.result}`;
          if (result.mode) {
            message += `
模式: ${result.mode}`;
          }
          if (result.data) {
            message += `
API数据: ${JSON.stringify(result.data, null, 2)}`;
          }
          if (result.apiError) {
            message += `
API错误: ${result.apiError}`;
          }
          uni.showModal({
            title: "扫码成功",
            content: message,
            showCancel: false
          });
        } else {
          scanStatus.value = `扫码失败: ${result.error}`;
          uni.showToast({
            title: result.error || "扫码失败",
            icon: "none"
          });
        }
      };
      const checkDevice = async () => {
        try {
          const configResult = await SunmiConfig.checkScannerConfig();
          deviceInfo.value = configResult.deviceInfo || {};
          if (configResult.success) {
            scannerType.value = SunmiConfig.isSunmiDevice() ? "商米设备" : "其他设备";
            scanStatus.value = "设备检查通过";
          } else {
            scannerType.value = "不支持的设备";
            scanStatus.value = configResult.error || "设备检查失败";
          }
        } catch (error) {
          formatAppLog("error", "at pages/test/scanner.vue:59", "检查设备失败:", error);
          scanStatus.value = "设备检查异常";
        }
      };
      const startScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "general");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "请使用扫码枪扫描",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `初始化失败: ${error.message}`;
        }
      };
      const startReportScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化上报扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "report");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "上报模式已启用，请扫描机器码",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `上报模式初始化失败: ${error.message}`;
        }
      };
      const startRepairScan = async () => {
        isScanning.value = true;
        scanStatus.value = "正在初始化维修扫码...";
        try {
          const initResult = await scannerUtils.initScanner(handleScanResult, true, "repair");
          scanStatus.value = initResult.message;
          if (initResult.success) {
            uni.showToast({
              title: "维修模式已启用，请扫描机器码",
              icon: "none",
              duration: 3e3
            });
          }
        } catch (error) {
          isScanning.value = false;
          scanStatus.value = `维修模式初始化失败: ${error.message}`;
        }
      };
      const manualScan = () => {
        scannerUtils.startScan();
      };
      const clearResult = () => {
        scanResult.value = "";
        scanStatus.value = "已清除";
        isScanning.value = false;
      };
      const configureSunmi = async () => {
        try {
          const result = await SunmiConfig.configureOutputMode();
          uni.showModal({
            title: "配置提示",
            content: result.message,
            showCancel: false
          });
        } catch (error) {
          uni.showToast({
            title: "配置失败",
            icon: "none"
          });
        }
      };
      vue.onMounted(async () => {
        scannerUtils.setPageActive(true);
        await checkDevice();
        startScan();
      });
      vue.onUnmounted(() => {
        scannerUtils.destroy();
      });
      const __returned__ = { scanResult, scanStatus, isScanning, deviceInfo, scannerType, handleScanResult, checkDevice, startScan, startReportScan, startRepairScan, manualScan, clearResult, configureSunmi };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "scanner-test" }, [
      vue.createElementVNode("view", { class: "header" }, [
        vue.createElementVNode("text", { class: "title" }, "商米扫码功能测试")
      ]),
      vue.createCommentVNode(" 设备信息卡片 "),
      vue.createElementVNode("view", { class: "device-card" }, [
        vue.createElementVNode("text", { class: "card-title" }, "设备信息"),
        vue.createElementVNode("view", { class: "device-info" }, [
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "设备类型：" + vue.toDisplayString($setup.scannerType),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "品牌：" + vue.toDisplayString($setup.deviceInfo.brand || "未知"),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "型号：" + vue.toDisplayString($setup.deviceInfo.model || "未知"),
            1
            /* TEXT */
          ),
          vue.createElementVNode(
            "text",
            { class: "info-item" },
            "系统：" + vue.toDisplayString($setup.deviceInfo.system || "未知"),
            1
            /* TEXT */
          )
        ]),
        vue.createElementVNode("button", {
          class: "config-btn",
          onClick: $setup.configureSunmi
        }, "扫码配置说明")
      ]),
      vue.createCommentVNode(" 状态卡片 "),
      vue.createElementVNode("view", { class: "status-card" }, [
        vue.createElementVNode("text", { class: "status-label" }, "状态："),
        vue.createElementVNode(
          "text",
          {
            class: vue.normalizeClass(["status-text", {
              "success": $setup.scanStatus.includes("成功"),
              "error": $setup.scanStatus.includes("失败") || $setup.scanStatus.includes("异常")
            }])
          },
          vue.toDisplayString($setup.scanStatus),
          3
          /* TEXT, CLASS */
        )
      ]),
      vue.createCommentVNode(" 结果卡片 "),
      $setup.scanResult ? (vue.openBlock(), vue.createElementBlock("view", {
        key: 0,
        class: "result-card"
      }, [
        vue.createElementVNode("text", { class: "result-label" }, "扫码结果："),
        vue.createElementVNode(
          "text",
          { class: "result-text" },
          vue.toDisplayString($setup.scanResult),
          1
          /* TEXT */
        )
      ])) : vue.createCommentVNode("v-if", true),
      vue.createCommentVNode(" 按钮组 "),
      vue.createElementVNode("view", { class: "button-group" }, [
        vue.createElementVNode("button", {
          class: "scan-btn primary",
          onClick: $setup.startScan,
          disabled: $setup.isScanning
        }, vue.toDisplayString($setup.isScanning ? "扫码中..." : "通用扫码测试"), 9, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn report",
          onClick: $setup.startReportScan,
          disabled: $setup.isScanning
        }, " 上报模式测试 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn repair",
          onClick: $setup.startRepairScan,
          disabled: $setup.isScanning
        }, " 维修模式测试 ", 8, ["disabled"]),
        vue.createElementVNode("button", {
          class: "scan-btn secondary",
          onClick: $setup.manualScan
        }, " 手动扫码(降级测试) "),
        $setup.scanResult ? (vue.openBlock(), vue.createElementBlock("button", {
          key: 0,
          class: "scan-btn danger",
          onClick: $setup.clearResult
        }, " 清除结果 ")) : vue.createCommentVNode("v-if", true)
      ]),
      vue.createCommentVNode(" 使用说明 "),
      vue.createElementVNode("view", { class: "info-card" }, [
        vue.createElementVNode("text", { class: "info-title" }, "使用说明："),
        vue.createElementVNode("text", { class: "info-text" }, "1. 商米PDA设备会优先使用商米扫码头"),
        vue.createElementVNode("text", { class: "info-text" }, "2. 如果商米扫码头不可用，会降级到新大陆扫码头"),
        vue.createElementVNode("text", { class: "info-text" }, "3. 最终降级方案是系统uni.scanCode"),
        vue.createElementVNode("text", { class: "info-text" }, "4. 上报/维修模式会调用后端API验证"),
        vue.createElementVNode("text", { class: "info-text" }, "5. 确保设备扫码配置中已开启广播输出")
      ])
    ]);
  }
  const PagesTestScanner = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "D:/learn/uniapptest/testvue3/src/pages/test/scanner.vue"]]);
  __definePage("pages/login/login", PagesLoginLogin);
  __definePage("pages/workspace/workspace", PagesWorkspaceWorkspace);
  __definePage("pages/profile/profile", PagesProfileProfile);
  __definePage("pages/worker/home", PagesWorkerHome);
  __definePage("pages/mechanic/home", PagesMechanicHome);
  __definePage("pages/report/report", PagesReportReport);
  __definePage("pages/repair/repair", PagesRepairRepair);
  __definePage("pages/test/scanner", PagesTestScanner);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
      const userInfo = uni.getStorageSync("userInfo");
      if (!userInfo) {
        uni.reLaunch({
          url: "/pages/login/login"
        });
      }
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:15", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:18", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "D:/learn/uniapptest/testvue3/src/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
