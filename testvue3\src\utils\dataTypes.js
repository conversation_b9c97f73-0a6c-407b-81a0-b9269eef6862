/**
 * 前端数据类型定义 - 对应后端模型结构
 */

import { DisplayNameUtils } from './constants.js'

/**
 * 用户数据类型
 */
export class User {
  constructor(data = {}) {
    this.id = data.id || data.worker_id || 0
    this.username = data.username || data.worker_name || ''
    this.worker_name = data.worker_name || ''
    this.worker_type = data.worker_type !== undefined ? data.worker_type : 0  // workerType: 0=织工, 1=机修工
    this.role = data.role !== undefined ? data.role : 0  // workerType: 0=织工, 1=机修工
    this.work_class = data.work_class || ''       // 工作类别字符串
    this.dept = data.dept || ''
    this.sex = data.sex || ''
    this.name = data.name || data.worker_name || ''
    this.created_at = data.created_at || ''
    this.updated_at = data.updated_at || ''
  }
  
  // 获取角色显示名称
  getRoleDisplayName() {
    return DisplayNameUtils.getUserRoleNameById(this.role === 'weaver' ? 0 : 1)
  }
  
  // 判断是否为织工 (workerType = 0)
  isWeaver() {
    return this.role === 'weaver'
  }
  
  // 判断是否为机修工 (workerType = 1)
  isMechanic() {
    return this.role === 'mechanic'
  }
  
  // 获取角色枚举值
  getRole() {
    return this.role === 'weaver' ? '织工' : (this.role === 'mechanic' ? '机修工' : 'unknown')
  }
}

/**
 * 机器数据类型
 */
export class Machine {
  constructor(data = {}) {
    this.id = data.id || 0
    this.code = data.code || ''
    this.name = data.name || ''
    this.location = data.location || ''
    this.status = data.status || 'normal'           // 状态枚举值
    this.status_name = data.status_name || ''       // 状态显示名称
    this.qr_code = data.qr_code || ''
    this.created_at = data.created_at || ''
    this.updated_at = data.updated_at || ''
  }
  
  // 获取状态显示名称（优先使用后端返回的status_name）
  getStatusDisplayName() {
    return this.status_name || DisplayNameUtils.getMachineStatusName(this.status)
  }
  
  // 判断机器状态
  isNormal() {
    return this.status === 'normal'
  }
  
  isAbnormal() {
    return this.status === 'abnormal'
  }
  
  isRepairing() {
    return this.status === 'repairing'
  }
}

/**
 * 异常数据类型
 */
export class Anomaly {
  constructor(data = {}) {
    this.id = data.id || 0
    this.machine_id = data.machine_id || 0
    this.user_id = data.user_id || 0
    this.description = data.description || ''
    this.severity = data.severity || 'medium'           // 严重程度枚举值
    this.severity_name = data.severity_name || ''       // 严重程度显示名称
    this.status = data.status || 'pending'              // 状态枚举值
    this.status_name = data.status_name || ''           // 状态显示名称
    this.remark = data.remark || ''
    this.created_at = data.created_at || ''
    this.updated_at = data.updated_at || ''
    
    // 关联数据
    this.machine = data.machine ? new Machine(data.machine) : null
    this.user = data.user ? new User(data.user) : null
  }
  
  // 获取严重程度显示名称
  getSeverityDisplayName() {
    return this.severity_name || DisplayNameUtils.getAnomalySeverityName(this.severity)
  }
  
  // 获取状态显示名称
  getStatusDisplayName() {
    return this.status_name || DisplayNameUtils.getAnomalyStatusName(this.status)
  }
  
  // 判断异常状态
  isPending() {
    return this.status === 'pending'
  }
  
  isRepairing() {
    return this.status === 'repairing'
  }
  
  isCompleted() {
    return this.status === 'completed'
  }
  
  // 判断严重程度
  isLowSeverity() {
    return this.severity === 'low'
  }
  
  isMediumSeverity() {
    return this.severity === 'medium'
  }
  
  isHighSeverity() {
    return this.severity === 'high'
  }
}

/**
 * 维修数据类型
 */
export class Repair {
  constructor(data = {}) {
    this.id = data.id || 0
    this.anomaly_id = data.anomaly_id || 0
    this.mechanic_id = data.mechanic_id || 0
    this.start_time = data.start_time || ''
    this.end_time = data.end_time || null
    this.process = data.process || ''
    this.parts = data.parts || ''
    this.duration = data.duration || 0
    this.status = data.status || 'in_progress'        // 状态枚举值
    this.status_name = data.status_name || ''         // 状态显示名称
    this.created_at = data.created_at || ''
    this.updated_at = data.updated_at || ''
    
    // 关联数据
    this.anomaly = data.anomaly ? new Anomaly(data.anomaly) : null
    this.mechanic = data.mechanic ? new User(data.mechanic) : null
  }
  
  // 获取状态显示名称
  getStatusDisplayName() {
    return this.status_name || DisplayNameUtils.getRepairStatusName(this.status)
  }
  
  // 判断维修状态
  isInProgress() {
    return this.status === 'in_progress'
  }
  
  isCompleted() {
    return this.status === 'completed'
  }
  
  // 获取格式化的维修时长
  getFormattedDuration() {
    if (!this.duration) return '0分钟'
    
    const hours = Math.floor(this.duration / 60)
    const minutes = this.duration % 60
    
    if (hours > 0) {
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`
    }
    return `${minutes}分钟`
  }
}

/**
 * API响应数据包装类
 */
export class ApiResponse {
  constructor(data = {}) {
    this.code = data.code || 0
    this.message = data.message || ''
    this.data = data.data || null
    this.total = data.total || 0
    this.page = data.page || 1
    this.size = data.size || 10
  }
  
  // 判断是否成功
  isSuccess() {
    return this.code === 200
  }
  
  // 获取数据
  getData() {
    return this.data
  }
  
  // 获取分页信息
  getPagination() {
    return {
      total: this.total,
      page: this.page,
      size: this.size,
      hasMore: this.page * this.size < this.total
    }
  }
}

/**
 * 数据转换工具
 */
export const DataUtils = {
  // 转换用户数据
  convertUser(data) {
    return new User(data)
  },
  
  // 转换用户列表
  convertUserList(dataList) {
    return Array.isArray(dataList) ? dataList.map(item => new User(item)) : []
  },
  
  // 转换机器数据
  convertMachine(data) {
    return new Machine(data)
  },
  
  // 转换机器列表
  convertMachineList(dataList) {
    return Array.isArray(dataList) ? dataList.map(item => new Machine(item)) : []
  },
  
  // 转换异常数据
  convertAnomaly(data) {
    return new Anomaly(data)
  },
  
  // 转换异常列表
  convertAnomalyList(dataList) {
    return Array.isArray(dataList) ? dataList.map(item => new Anomaly(item)) : []
  },
  
  // 转换维修数据
  convertRepair(data) {
    return new Repair(data)
  },
  
  // 转换维修列表
  convertRepairList(dataList) {
    return Array.isArray(dataList) ? dataList.map(item => new Repair(item)) : []
  },
  
  // 转换API响应
  convertApiResponse(data) {
    return new ApiResponse(data)
  }
}
