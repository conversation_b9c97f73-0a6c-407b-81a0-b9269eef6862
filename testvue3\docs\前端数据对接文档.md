# 前端数据类型和常量对接文档

本文档说明了如何使用新创建的前端数据类型和常量来与后端API进行对接。

## 📁 文件结构

```
src/utils/
├── constants.js      # 前端常量定义（对应后端枚举）
├── dataTypes.js      # 前端数据类型定义（对应后端模型）
├── usageExamples.js  # 使用示例代码
└── api.js           # 已有的API接口定义
```

## 🔧 核心功能

### 1. 数据类型转换 (dataTypes.js)

#### 用户类型 (User)
```javascript
import { DataUtils } from '@/utils/dataTypes'

// 转换单个用户
const user = DataUtils.convertUser(apiResponse.data.user)

// 使用用户方法
console.log(user.getRoleDisplayName()) // "织工" 或 "机修工"
console.log(user.isWeaver())          // true/false
console.log(user.isMechanic())        // true/false
```

#### 机器类型 (Machine)
```javascript
// 转换机器列表
const machines = DataUtils.convertMachineList(apiResponse.data)

// 使用机器方法
machines.forEach(machine => {
  console.log(machine.getStatusDisplayName()) // "正常"、"异常"、"维修中"
  console.log(machine.isNormal())            // true/false
  console.log(machine.isAbnormal())          // true/false
})
```

#### 异常类型 (Anomaly)
```javascript
// 转换异常数据
const anomaly = DataUtils.convertAnomaly(apiResponse.data)

// 使用异常方法
console.log(anomaly.getSeverityDisplayName()) // "低"、"中"、"高"
console.log(anomaly.getStatusDisplayName())   // "待维修"、"维修中"、"已完成"
console.log(anomaly.isPending())              // true/false
```

#### 维修类型 (Repair)
```javascript
// 转换维修数据
const repair = DataUtils.convertRepair(apiResponse.data)

// 使用维修方法
console.log(repair.getStatusDisplayName())   // "进行中"、"已完成"
console.log(repair.getFormattedDuration())   // "2小时30分钟"
console.log(repair.isInProgress())           // true/false
```

### 2. 常量定义 (constants.js)

#### 枚举值常量
```javascript
import { 
  USER_ROLES,
  MACHINE_STATUS,
  ANOMALY_SEVERITY,
  ANOMALY_STATUS,
  REPAIR_STATUS
} from '@/utils/constants'

// 使用枚举值
const newAnomaly = {
  severity: ANOMALY_SEVERITY.HIGH,  // "high"
  status: ANOMALY_STATUS.PENDING    // "pending"
}
```

#### 显示名称映射
```javascript
import { DisplayNameUtils } from '@/utils/constants'

// 获取显示名称
console.log(DisplayNameUtils.getUserRoleName('weaver'))      // "织工"
console.log(DisplayNameUtils.getMachineStatusName('normal')) // "正常"
```

#### 表单选项数据
```javascript
import { OPTIONS } from '@/utils/constants'

// 在Vue组件中使用
export default {
  data() {
    return {
      severityOptions: OPTIONS.anomalySeverity,
      // 输出: [
      //   { value: 'low', label: '低' },
      //   { value: 'medium', label: '中' },
      //   { value: 'high', label: '高' }
      // ]
    }
  }
}
```

#### 数据验证
```javascript
import { ValidationUtils } from '@/utils/constants'

// 验证数据有效性
if (!ValidationUtils.isValidUserRole(role)) {
  console.error('无效的用户角色')
}
```

## 🚀 实际使用场景

### 场景1: 登录页面
```javascript
// pages/login/login.vue
import { DataUtils } from '@/utils/dataTypes'

const handleLogin = async () => {
  const response = await API.auth.login(loginForm)
  
  if (response.code === 200) {
    // 转换用户数据
    const user = DataUtils.convertUser(response.data.user)
    
    // 保存用户信息
    uni.setStorageSync('userInfo', user)
    
    // 显示欢迎信息
    uni.showToast({
      title: `登录成功，欢迎${user.getRoleDisplayName()}${user.name}`,
      icon: 'success'
    })
  }
}
```

### 场景2: 工作台页面
```javascript
// pages/workspace/workspace.vue
import { DataUtils, User } from '@/utils/dataTypes'

const userInfo = ref<User | null>(null)

const getUserInfo = () => {
  const info = uni.getStorageSync('userInfo')
  if (info) {
    userInfo.value = DataUtils.convertUser(info)
    
    // 根据角色设置不同功能
    if (userInfo.value.isWeaver()) {
      // 织工功能
    } else if (userInfo.value.isMechanic()) {
      // 机修工功能
    }
  }
}
```

### 场景3: 异常上报页面
```javascript
// pages/report/report.vue
import { OPTIONS, ValidationUtils } from '@/utils/constants'
import { DataUtils } from '@/utils/dataTypes'

export default {
  data() {
    return {
      // 使用预定义的选项
      severityOptions: OPTIONS.anomalySeverity,
      form: {
        severity: '',
        description: ''
      }
    }
  },
  
  methods: {
    async submitReport() {
      // 验证数据
      if (!ValidationUtils.isValidAnomalySeverity(this.form.severity)) {
        uni.showToast({ title: '请选择严重程度', icon: 'none' })
        return
      }
      
      const response = await API.anomalies.create(this.form)
      if (response.code === 200) {
        const anomaly = DataUtils.convertAnomaly(response.data)
        uni.showToast({
          title: `异常上报成功，严重程度: ${anomaly.getSeverityDisplayName()}`,
          icon: 'success'
        })
      }
    }
  }
}
```

### 场景4: 机器状态显示
```javascript
// 在列表页面显示机器状态
<template>
  <view v-for="machine in machines" :key="machine.id">
    <text>{{ machine.name }}</text>
    <text :style="{ color: getStatusColor(machine.status) }">
      {{ machine.getStatusDisplayName() }}
    </text>
  </view>
</template>

<script>
import { DataUtils } from '@/utils/dataTypes'
import { MACHINE_STATUS } from '@/utils/constants'

export default {
  data() {
    return {
      machines: []
    }
  },
  
  methods: {
    async loadMachines() {
      const response = await API.machines.getList()
      if (response.code === 200) {
        this.machines = DataUtils.convertMachineList(response.data)
      }
    },
    
    getStatusColor(status) {
      switch(status) {
        case MACHINE_STATUS.NORMAL: return '#4CAF50'
        case MACHINE_STATUS.ABNORMAL: return '#FF9800' 
        case MACHINE_STATUS.REPAIRING: return '#2196F3'
        default: return '#999999'
      }
    }
  }
}
</script>
```

## 🔄 数据流转示例

### 完整的异常处理流程

```javascript
// 1. 扫码获取机器信息
const scanResult = await API.qr.scanForReport(qrCode)
const machine = DataUtils.convertMachine(scanResult.data.machine)

// 2. 创建异常上报
const anomalyData = {
  machine_id: machine.id,
  severity: ANOMALY_SEVERITY.HIGH,
  description: '机器异响严重'
}

const createResponse = await API.anomalies.create(anomalyData)
const anomaly = DataUtils.convertAnomaly(createResponse.data)

// 3. 机修工开始维修
const repairResponse = await API.repairs.start({
  anomaly_id: anomaly.id
})
const repair = DataUtils.convertRepair(repairResponse.data)

// 4. 完成维修
const completeResponse = await API.repairs.complete(repair.id, {
  process: '更换轴承',
  parts: '轴承x2',
  duration: 120 // 分钟
})
const completedRepair = DataUtils.convertRepair(completeResponse.data)

console.log(`维修完成，耗时: ${completedRepair.getFormattedDuration()}`)
```

## ✅ 最佳实践

1. **总是使用数据转换**: 从API获取数据后立即转换为对应的数据类型
2. **利用类型方法**: 使用内置的判断方法而不是直接比较字符串
3. **使用常量**: 避免硬编码字符串，使用预定义的常量
4. **数据验证**: 在提交数据前使用验证工具
5. **统一错误处理**: 使用统一的错误处理机制

## 🎯 后端对应关系

| 前端文件 | 后端文件 | 说明 |
|---------|---------|------|
| `constants.js` | `constants/enums.go` | 枚举值和显示名称 |
| `dataTypes.js` | `models/models.go` | 数据模型结构 |
| `User` 类 | `User` 结构体 | 包含 role, role_id, role_name |
| `Machine` 类 | `Machine` 结构体 | 包含 status, status_name |
| `Anomaly` 类 | `Anomaly` 结构体 | 包含 severity, severity_name, status, status_name |
| `Repair` 类 | `Repair` 结构体 | 包含 status, status_name |

## 📝 更新现有页面的步骤

1. **导入所需模块**:
   ```javascript
   import { DataUtils, User, Machine } from '@/utils/dataTypes'
   import { DisplayNameUtils, OPTIONS } from '@/utils/constants'
   ```

2. **更新数据处理逻辑**:
   - 使用 `DataUtils.convertXxx()` 转换API响应
   - 使用类型方法替代字符串比较

3. **更新模板显示**:
   - 使用 `getXxxDisplayName()` 方法显示中文名称
   - 使用类型判断方法进行条件渲染

4. **更新表单选项**:
   - 使用 `OPTIONS` 中的预定义选项
   - 使用 `ValidationUtils` 进行数据验证

通过这套数据类型和常量系统，前端可以更好地与后端API对接，确保数据的一致性和类型安全。